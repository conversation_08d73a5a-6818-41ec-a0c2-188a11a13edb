"""
LLM提示词构建器
根据战场数据、武器知识库和我方信息构建大模型提示词
"""
import json
import time
from typing import Dict, Any, List, Optional
import logging


class LLMPromptBuilder:
    """LLM提示词构建器"""
    
    def __init__(self):
        """初始化提示词构建器"""
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 提示词模板
        self.system_prompt_template = """你是一名资深的军事战术分析专家，具有丰富的现代战争经验和武器装备知识。
你的任务是分析战场态势，评估敌方威胁，并制定有效的作战方案。

请基于提供的情报信息，进行专业的军事分析，并给出具体可行的作战建议。

分析要求：
1. 客观评估敌方武器的威胁程度
2. 分析我方的优势和劣势
3. 制定针对性的作战策略
4. 考虑地形、天气等环境因素
5. 提供具体的兵力部署建议
6. 评估作战风险和成功概率

输出格式要求：
请以JSON格式输出分析结果，包含以下字段：
- threat_assessment: 威胁评估
- tactical_analysis: 战术分析  
- recommended_strategy: 推荐策略
- force_deployment: 兵力部署
- risk_assessment: 风险评估
- success_probability: 成功概率"""

        self.user_prompt_template = """
# 战场情报分析任务

## 敌方情报
{enemy_intelligence}

## 我方部队信息
{friendly_forces_info}

## 任务目标
{mission_objective}

## 环境信息
{environmental_info}

## 武器技术分析
{weapon_technical_analysis}

请基于以上信息，制定击败敌方的最佳作战方案。
"""
        
        self.logger.info("LLM提示词构建器初始化完成")
    
    def build_enemy_intelligence_section(self, battlefield_data: Dict, weapon_knowledge: Dict, 
                                       counter_relationships: Dict) -> str:
        """构建敌方情报部分"""
        
        battlefield_info = battlefield_data.get("battlefield_data") or {}
        weapon_data = weapon_knowledge.get("weapon_data") or {} if weapon_knowledge and weapon_knowledge.get("status") == "found" else {}
        
        enemy_intel = {
            "detected_weapon": {
                "id": battlefield_info.get("id", "unknown"),
                "name": battlefield_info.get("weapon_name", "未知武器"),
                "type": battlefield_info.get("weapon_type", "未知类型"),
                "faction": battlefield_info.get("faction", "敌方"),
                "location": {
                    "longitude": battlefield_info.get("longitude"),
                    "latitude": battlefield_info.get("latitude"),
                    "altitude": battlefield_info.get("altitude")
                }
            }
        }
        
        # 添加武器性能信息
        if weapon_data:
            enemy_intel["weapon_capabilities"] = {
                "max_range_km": weapon_data.get("max_range"),
                "max_speed_kmh": weapon_data.get("max_speed"),
                "armor_thickness_mm": weapon_data.get("armor_thickness"),
                "main_weapon": weapon_data.get("main_weapon"),
                "crew_count": weapon_data.get("crew_count"),
                "weight_tons": weapon_data.get("weight"),
                "ratings": {
                    "firepower": weapon_data.get("firepower_rating"),
                    "protection": weapon_data.get("protection_rating"),
                    "mobility": weapon_data.get("mobility_rating"),
                    "detection": weapon_data.get("detection_rating")
                },
                "advantages": weapon_data.get("advantages"),
                "weaknesses": weapon_data.get("weaknesses")
            }
        
        # 添加克制关系信息
        if counter_relationships and isinstance(counter_relationships, dict):
            enemy_intel["threat_analysis"] = {
                "can_effectively_engage": counter_relationships.get("effective_against", []),
                "vulnerable_to": counter_relationships.get("vulnerable_to", [])
            }
        
        return json.dumps(enemy_intel, ensure_ascii=False, indent=2)
    
    def build_friendly_forces_section(self, friendly_forces: List[Dict]) -> str:
        """构建我方部队信息部分"""
        
        forces_info = {
            "available_units": [],
            "total_personnel": 0,
            "total_vehicles": 0,
            "unit_types": set(),
            "readiness_status": "ready"
        }
        
        for unit in friendly_forces:
            unit_info = {
                "unit_id": unit.get("unit_id"),
                "unit_name": unit.get("unit_name"),
                "unit_type": unit.get("unit_type"),
                "weapon_systems": unit.get("weapon_systems", []),
                "personnel": unit.get("personnel_count", 0),
                "vehicles": unit.get("vehicle_count", 0),
                "location": unit.get("current_location"),
                "status": unit.get("operational_status"),
                "supply_level": unit.get("supply_level"),
                "morale": unit.get("morale_level")
            }
            
            forces_info["available_units"].append(unit_info)
            forces_info["total_personnel"] += unit.get("personnel_count", 0)
            forces_info["total_vehicles"] += unit.get("vehicle_count", 0)
            forces_info["unit_types"].add(unit.get("unit_type"))
        
        forces_info["unit_types"] = list(forces_info["unit_types"])
        
        return json.dumps(forces_info, ensure_ascii=False, indent=2)
    
    def build_mission_objective_section(self, battlefield_data: Dict) -> str:
        """构建任务目标部分"""
        
        battlefield_info = battlefield_data.get("battlefield_data") or {}
        threat_level = (battlefield_data.get("battlefield_analysis") or {}).get("threat_level", "unknown")
        
        mission_info = {
            "primary_objective": "消除敌方威胁，确保战场控制",
            "target_details": {
                "target_id": battlefield_info.get("id"),
                "target_type": battlefield_info.get("weapon_type"),
                "threat_level": threat_level,
                "location": {
                    "longitude": battlefield_info.get("longitude"),
                    "latitude": battlefield_info.get("latitude"),
                    "altitude": battlefield_info.get("altitude")
                }
            },
            "mission_constraints": [
                "最小化己方伤亡",
                "避免附带损伤",
                "确保任务成功率",
                "考虑后续作战需求"
            ],
            "success_criteria": [
                "目标被成功摧毁或中和",
                "我方部队安全撤离",
                "战场态势得到控制",
                "为后续行动创造条件"
            ]
        }
        
        return json.dumps(mission_info, ensure_ascii=False, indent=2)
    
    def build_environmental_section(self, battlefield_data: Dict, weapon_knowledge: Dict) -> str:
        """构建环境信息部分"""
        
        battlefield_info = battlefield_data.get("battlefield_data") or {}
        weapon_data = weapon_knowledge.get("weapon_data") or {} if weapon_knowledge and weapon_knowledge.get("status") == "found" else {}
        
        env_info = {
            "location_info": {
                "coordinates": {
                    "longitude": battlefield_info.get("longitude"),
                    "latitude": battlefield_info.get("latitude"),
                    "altitude": battlefield_info.get("altitude")
                },
                "terrain_type": "需要根据坐标分析",
                "urban_density": "需要评估"
            },
            "weather_considerations": weapon_data.get("weather_limitations", {}),
            "terrain_adaptability": weapon_data.get("terrain_adaptability", {}),
            "operational_factors": [
                "能见度条件",
                "通信环境",
                "补给线路",
                "撤退路线"
            ]
        }
        
        return json.dumps(env_info, ensure_ascii=False, indent=2)
    
    def build_weapon_technical_analysis(self, weapon_knowledge: Dict, counter_relationships: Dict) -> str:
        """构建武器技术分析部分"""
        
        weapon_data = weapon_knowledge.get("weapon_data") or {} if weapon_knowledge and weapon_knowledge.get("status") == "found" else {}
        
        if not weapon_data:
            return json.dumps({"status": "武器信息未找到，需要基于武器类型进行通用分析"}, ensure_ascii=False, indent=2)
        
        technical_analysis = {
            "weapon_specifications": {
                "basic_info": {
                    "name": weapon_data.get("weapon_name"),
                    "type": weapon_data.get("weapon_type"),
                    "country": weapon_data.get("country"),
                    "manufacturer": weapon_data.get("manufacturer")
                },
                "performance_metrics": {
                    "max_range_km": weapon_data.get("max_range"),
                    "max_speed_kmh": weapon_data.get("max_speed"),
                    "armor_thickness_mm": weapon_data.get("armor_thickness"),
                    "main_weapon": weapon_data.get("main_weapon"),
                    "crew_count": weapon_data.get("crew_count"),
                    "weight_tons": weapon_data.get("weight")
                },
                "combat_ratings": {
                    "firepower": weapon_data.get("firepower_rating"),
                    "protection": weapon_data.get("protection_rating"),
                    "mobility": weapon_data.get("mobility_rating"),
                    "detection": weapon_data.get("detection_rating")
                }
            },
            "tactical_characteristics": {
                "strengths": weapon_data.get("advantages"),
                "weaknesses": weapon_data.get("weaknesses"),
                "recommended_tactics": weapon_data.get("recommended_tactics"),
                "counter_tactics": weapon_data.get("counter_tactics")
            },
            "engagement_matrix": {
                "effective_against": counter_relationships.get("effective_against", []) if counter_relationships and isinstance(counter_relationships, dict) else [],
                "vulnerable_to": counter_relationships.get("vulnerable_to", []) if counter_relationships and isinstance(counter_relationships, dict) else []
            },
            "technical_specifications": weapon_data.get("technical_specs", {})
        }
        
        return json.dumps(technical_analysis, ensure_ascii=False, indent=2)
    
    def build_complete_prompt(self, battlefield_data: Dict) -> Dict[str, str]:
        """
        构建完整的LLM提示词
        
        Args:
            battlefield_data: 完整的战场处理数据
            
        Returns:
            包含system_prompt和user_prompt的字典
        """
        try:
            # 提取各部分数据，确保不为None
            weapon_knowledge = battlefield_data.get("weapon_knowledge") or {}
            counter_relationships = battlefield_data.get("counter_relationships") or {}
            friendly_forces = battlefield_data.get("friendly_forces") or []
            
            # 构建各个部分
            enemy_intelligence = self.build_enemy_intelligence_section(
                battlefield_data, weapon_knowledge, counter_relationships
            )
            
            friendly_forces_info = self.build_friendly_forces_section(friendly_forces)
            
            mission_objective = self.build_mission_objective_section(battlefield_data)
            
            environmental_info = self.build_environmental_section(battlefield_data, weapon_knowledge)
            
            weapon_technical_analysis = self.build_weapon_technical_analysis(
                weapon_knowledge, counter_relationships
            )
            
            # 构建用户提示词
            user_prompt = self.user_prompt_template.format(
                enemy_intelligence=enemy_intelligence,
                friendly_forces_info=friendly_forces_info,
                mission_objective=mission_objective,
                environmental_info=environmental_info,
                weapon_technical_analysis=weapon_technical_analysis
            )
            
            self.logger.info("LLM提示词构建完成")
            
            return {
                "system_prompt": self.system_prompt_template,
                "user_prompt": user_prompt,
                "prompt_metadata": {
                    "generated_at": time.time(),
                    "battlefield_id": (battlefield_data.get("battlefield_data") or {}).get("id"),
                    "weapon_name": (battlefield_data.get("battlefield_data") or {}).get("weapon_name"),
                    "threat_level": (battlefield_data.get("battlefield_analysis") or {}).get("threat_level"),
                    "prompt_length": len(user_prompt),
                    "has_weapon_knowledge": weapon_knowledge.get("status") == "found",
                    "friendly_units_count": len(friendly_forces)
                }
            }
            
        except Exception as e:
            self.logger.error(f"提示词构建失败: {str(e)}")
            raise Exception(f"LLM提示词构建失败: {str(e)}")
    
    def get_prompt_summary(self, prompt_data: Dict) -> Dict[str, Any]:
        """获取提示词摘要信息"""
        metadata = prompt_data.get("prompt_metadata", {})

        return {
            "battlefield_id": metadata.get("battlefield_id"),
            "weapon_name": metadata.get("weapon_name"),
            "threat_level": metadata.get("threat_level"),
            "prompt_length": metadata.get("prompt_length"),
            "has_weapon_knowledge": metadata.get("has_weapon_knowledge"),
            "friendly_units_count": metadata.get("friendly_units_count"),
            "generated_at": metadata.get("generated_at")
        }

    def build_comprehensive_battle_prompt(self, battlefield_data: Dict[str, Any]) -> Dict[str, str]:
        """
        构建综合战场分析提示词

        Args:
            battlefield_data: 包含敌我双方单位和知识库信息的综合数据

        Returns:
            包含系统提示词和用户提示词的字典
        """
        self.logger.info("构建综合战场分析提示词")

        # 系统提示词
        system_prompt = """你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。"""

        # 提取战场数据
        scenario_data = battlefield_data.get("battlefield_data", {})
        enemy_units = scenario_data.get("enemy_units", [])
        friendly_units = scenario_data.get("friendly_units", [])
        enemy_knowledge = battlefield_data.get("enemy_knowledge", [])
        friendly_knowledge = battlefield_data.get("friendly_knowledge", [])
        tactical_situation = battlefield_data.get("tactical_situation", {})

        # 构建用户提示词
        user_prompt = f"""请分析以下战场态势：

## 战场环境
- 地形: {tactical_situation.get('terrain', '未知')}
- 天气: {tactical_situation.get('weather', '未知')}
- 能见度: {tactical_situation.get('visibility', '未知')}
- 时间: {tactical_situation.get('time_of_day', '未知')}

## 敌方单位 ({len(enemy_units)}个)
"""

        # 添加敌方单位详情
        for i, unit in enumerate(enemy_units, 1):
            # 获取位置信息
            position = unit.get('position', {})
            longitude = position.get('longitude', 0)
            latitude = position.get('latitude', 0)
            altitude = position.get('altitude', 0)

            user_prompt += f"""
### 敌方单位 {i}: {unit.get('name', '未知')}
- 类型: {unit.get('type', '未知')}
- 位置: 经度{longitude}, 纬度{latitude}, 高度{altitude}m
- 威胁等级: {unit.get('threat_level', '未知')}
- 置信度: {unit.get('confidence', 0)}
"""

            # 添加对应的武器知识
            if i <= len(enemy_knowledge):
                knowledge = enemy_knowledge[i-1].get("result", {})
                if knowledge.get("status") == "found":
                    weapon_data = knowledge.get("weapon_data", {})
                    user_prompt += f"""
- 性能数据:
  * 火力评级: {weapon_data.get('firepower_rating', '未知')}/10
  * 防护评级: {weapon_data.get('protection_rating', '未知')}/10
  * 机动评级: {weapon_data.get('mobility_rating', '未知')}/10
  * 主要武器: {weapon_data.get('main_gun', '未知')}
  * 最大速度: {weapon_data.get('max_speed', '未知')}
"""

        user_prompt += f"""

## 我方单位 ({len(friendly_units)}个)
"""

        # 添加我方单位详情
        for i, unit in enumerate(friendly_units, 1):
            # 获取位置信息
            position = unit.get('position', {})
            longitude = position.get('longitude', 0)
            latitude = position.get('latitude', 0)
            altitude = position.get('altitude', 0)

            # 获取状态信息
            status = unit.get('status', {})
            health = status.get('health', 0)
            ammo = status.get('ammo', 0)
            fuel = status.get('fuel', 0)
            operational = status.get('operational', False)

            user_prompt += f"""
### 我方单位 {i}: {unit.get('name', '未知')}
- 类型: {unit.get('type', '未知')}
- 位置: 经度{longitude}, 纬度{latitude}, 高度{altitude}m
- 状态: {'可作战' if operational else '不可作战'}
- 健康度: {health}%
- 弹药状态: {ammo}%
- 燃料状态: {fuel}%
"""

            # 添加对应的武器知识
            if i <= len(friendly_knowledge):
                knowledge = friendly_knowledge[i-1].get("result", {})
                if knowledge.get("status") == "found":
                    weapon_data = knowledge.get("weapon_data", {})
                    user_prompt += f"""
- 性能数据:
  * 火力评级: {weapon_data.get('firepower_rating', '未知')}/10
  * 防护评级: {weapon_data.get('protection_rating', '未知')}/10
  * 机动评级: {weapon_data.get('mobility_rating', '未知')}/10
  * 主要武器: {weapon_data.get('main_gun', '未知')}
  * 最大速度: {weapon_data.get('max_speed', '未知')}
"""

        user_prompt += """

## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。"""

        self.logger.info("综合战场分析提示词构建完成")

        return {
            "system_prompt": system_prompt,
            "user_prompt": user_prompt
        }
