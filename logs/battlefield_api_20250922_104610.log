2025-09-22 10:46:10,329 - __mp_main__ - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250922_104610.log
2025-09-22 10:46:10,582 - main_fastapi - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250922_104610.log
2025-09-22 10:46:10,582 - main_fastapi - INFO - 战场态势分析API服务启动
2025-09-22 10:46:10,927 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 1
2025-09-22 10:46:10,928 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 2
2025-09-22 10:46:10,955 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 3
2025-09-22 10:46:10,956 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 4
2025-09-22 10:46:10,987 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 5
2025-09-22 10:46:10,987 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 6
2025-09-22 10:46:11,018 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 7
2025-09-22 10:46:11,019 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 8
2025-09-22 10:46:11,047 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 9
2025-09-22 10:46:11,047 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 10
2025-09-22 10:46:11,077 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 11
2025-09-22 10:46:11,079 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 12
2025-09-22 10:46:11,107 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 13
2025-09-22 10:46:11,108 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 14
2025-09-22 10:46:11,138 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 15
2025-09-22 10:46:12,739 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 16
2025-09-22 10:46:12,740 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 17
2025-09-22 10:46:14,738 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 18
2025-09-22 10:46:14,739 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 19
2025-09-22 10:46:28,740 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 20
2025-09-22 10:46:28,741 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 21
2025-09-22 10:47:14,813 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 22
2025-09-22 10:47:14,817 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 23
2025-09-22 10:47:19,028 - main_fastapi - INFO - ================================================================================
2025-09-22 10:47:19,028 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758509239028
2025-09-22 10:47:19,028 - main_fastapi - INFO - 模拟时间: 60.0分钟
2025-09-22 10:47:19,028 - main_fastapi - INFO - ================================================================================
2025-09-22 10:47:19,028 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 10:47:19,028 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 10:47:19,028 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 116.65198974114053,
    "latitude": 39.94350233637679,
    "altitude": 45.2
  },
  "status": {
    "health": 90.03150116396401,
    "ammo": 71.89021242069762,
    "fuel": 65.86290147290407,
    "operational": true
  },
  "threat_level": "高",
  "confidence": 0.5058590240902551,
  "last_seen": "2025-09-22T10:47:18.762297",
  "speed": 25.0,
  "heading": 108.52204837696888
}
2025-09-22 10:47:19,028 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.64398831421595,
    "latitude": 39.677015149049446,
    "altitude": 52.1
  },
  "status": {
    "health": 100.0,
    "ammo": 59.133398222444434,
    "fuel": 58.15171069643849,
    "operational": true
  },
  "threat_level": "中",
  "confidence": 0.7481113387011357,
  "last_seen": "2025-09-22T10:47:18.762345",
  "speed": 35.0,
  "heading": 121.54877392255368
}
2025-09-22 10:47:19,028 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 107.68805219367769,
    "latitude": 42.135252479467,
    "altitude": 8760.697658686831
  },
  "status": {
    "health": 90.8014592693998,
    "ammo": 80.43940713208907,
    "fuel": 51.312055164337465,
    "operational": true
  },
  "threat_level": "极高",
  "confidence": 0.5879161671307811,
  "last_seen": "2025-09-22T10:47:18.762378",
  "speed": 800.0,
  "heading": 281.41432144717305
}
2025-09-22 10:47:19,028 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 10:47:19,028 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 116.24433690012445,
    "latitude": 40.138746007814966,
    "altitude": 48.5
  },
  "status": {
    "health": 61.830053926008105,
    "ammo": 86.0277576074384,
    "fuel": 63.01748495036562,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:47:18.762405",
  "speed": 30.0,
  "heading": 335.5471970373824
}
2025-09-22 10:47:19,028 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 115.19200501192745,
    "latitude": 32.32230348442273,
    "altitude": 9104.735989208086
  },
  "status": {
    "health": 100.0,
    "ammo": 91.3182187277115,
    "fuel": 57.578378593132854,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:47:18.762434",
  "speed": 900.0,
  "heading": 206.38939522891155
}
2025-09-22 10:47:19,028 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 10:47:19,028 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 10:47:19,378 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:47:19,378 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:47:19,381 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 10:47:19,381 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 352.28ms
2025-09-22 10:47:19,381 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:47:19,386 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 10:47:19,715 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:47:19,715 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:47:19,717 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 10:47:19,718 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 331.92ms
2025-09-22 10:47:19,718 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:47:19,722 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 10:47:20,045 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:47:20,045 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:47:20,047 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 10:47:20,047 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 325.68ms
2025-09-22 10:47:20,047 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 10:47:20,051 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 10:47:20,376 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:47:20,376 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:47:20,379 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 10:47:20,379 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 327.44ms
2025-09-22 10:47:20,379 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 10:47:20,383 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 10:47:20,713 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:47:20,713 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:47:20,715 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 10:47:20,715 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 332.76ms
2025-09-22 10:47:20,715 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:47:20,719 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 10:47:20,721 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 10:47:20,722 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 10:47:20,722 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 10:47:20,722 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 10:47:20,722 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.6ms
2025-09-22 10:47:20,722 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 10:47:20,722 - main_fastapi - INFO - 用户提示词长度: 2071 字符
2025-09-22 10:47:20,722 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 10:47:20,722 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 10:47:20,772 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 10:47:20,773 - main_fastapi - INFO - LLM服务配置:
2025-09-22 10:47:20,773 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 10:47:20,773 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 10:47:20,773 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 10:47:20,773 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 10:47:20,773 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 10:47:20,773 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 10:47:20,773 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度116.65198974114053, 纬度39.94350233637679, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5058590240902551

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.64398831421595, 纬度39.677015149049446, 高度52.1m
- 威胁等级: 中
- 置信度: 0.7481113387011357

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度107.68805219367769, 纬度42.135252479467, 高度8760.697658686831m
- 威胁等级: 极高
- 置信度: 0.5879161671307811


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度116.24433690012445, 纬度40.138746007814966, 高度48.5m
- 状态: 可作战
- 健康度: 61.830053926008105%
- 弹药状态: 86.0277576074384%
- 燃料状态: 63.01748495036562%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度115.19200501192745, 纬度32.32230348442273, 高度9104.735989208086m
- 状态: 可作战
- 健康度: 100.0%
- 弹药状态: 91.3182187277115%
- 燃料状态: 57.578378593132854%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 10:47:20,773 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 10:47:20,773 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 10:47:20,773 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 10:47:49,741 - src.llm_service - INFO - LLM分析完成 - 耗时: 28968.19ms, 消耗tokens: 2759
2025-09-22 10:47:49,741 - main_fastapi - INFO - 大模型分析成功 - 耗时: 29019.09ms
2025-09-22 10:47:49,741 - main_fastapi - INFO - 消耗tokens: 2759
2025-09-22 10:47:49,741 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 10:47:49,741 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 10:47:49,741 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "苏-35战斗机具备强大的空中打击能力，能够对我方地面部队构成严重威胁；T-90主战坦克拥有强大的火力和防护，对地面战斗形成直接威胁。",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "苏-35战斗机的高空作战能力和超视距打击能力",
      "T-90主战坦克的火力与防护性能",
      "BTR-80装甲运兵车的高机动性和两栖能力"
    ],
    "我方优势": [
      "歼-20战斗机的隐身能力和超音速巡航能力",
      "99A主战坦克的先进火控系统和高机动性",
      "平原地形有利于我方机械化部队快速机动"
    ],
    "关键弱点": [
      "敌方苏-35战斗机对我方地面部队的空中威胁",
      "敌方T-90主战坦克在正面交战中的强大火力和防护",
      "我方99A主战坦克健康度和弹药状态略显不足"
    ]
  },
  "战术建议": {
    "推荐策略": "先通过歼-20战斗机压制敌方空中力量，再利用99A主战坦克的机动性与火力优势，对敌方地面目标进行快速打击。",
    "优先目标": "苏-35战斗机（优先摧毁敌方空中威胁）",
    "兵力部署": "歼-20战斗机立即升空，前往敌方战斗机所在区域，尝试超视距攻击或隐身突防；99A主战坦克向敌方T-90主战坦克靠近，利用机动性寻找侧翼突破机会。",
    "注意事项": [
      "密切监控敌方战斗机的动态，避免被其发现并锁定",
      "99A主战坦克需注意保持隐蔽，避免过早暴露于敌方T-90主战坦克的射程内",
      "随时准备应对敌方装甲运兵车可能发起的快速机动或步兵渗透"
    ]
  },
  "作战方案": [
    {
      "方案名称": "空中优先压制",
      "执行步骤": [
        "歼-20战斗机迅速升空，利用隐身特性接近敌方苏-35战斗机，实施超视距攻击，必要时使用电子干扰手段降低其探测能力。",
        "歼-20战斗机在压制敌方战斗机的同时，保持与地面指挥中心的通信，实时共享战场情报。",
        "99A主战坦克在空中掩护下，向敌方T-90主战坦克靠近，利用地形遮蔽，寻找侧翼或后方突破口，实施精确打击。",
        "一旦敌方战斗机被压制，99A主战坦克可集中火力打击敌方地面目标。"
      ],
      "成功概率": "75%",
      "风险评估": "歼-20战斗机可能面临敌方防空系统的拦截，以及苏-35战斗机的反击；99A主战坦克在接近敌方坦克时可能暴露于其火力范围内。"
    },
    {
      "方案名称": "地面佯攻+空中支援",
      "执行步骤": [
        "99A主战坦克采取佯动战术，从多个方向模拟进攻，吸引敌方注意力，特别是敌方T-90主战坦克。",
        "歼-20战斗机在高空待命，观察敌方战斗机的动态，同时为地面部队提供实时情报支持。",
        "当敌方战斗机试图支援地面部队时，歼-20战斗机迅速出击，优先摧毁敌方战斗机。",
        "随后，99A主战坦克抓住战机，集中火力对敌方地面目标实施致命打击。"
      ],
      "成功概率": "65%",
      "风险评估": "地面佯动可能导致99A主战坦克暴露于敌方坦克的火力范围，增加损失风险；歼-20战斗机可能因敌方战斗机的反击而受损。"
    }
  ],
  "应急预案": {
    "撤退路线": "99A主战坦克向西北方向撤退至预设阵地，歼-20战斗机则返回基地或转入隐蔽状态。",
    "支援需求": "请求增派无人机侦察敌方动向，以及呼叫远程火力支援（如陆基导弹）打击敌方重要目标。",
    "备用方案": "若空中压制失败，则优先保护99A主战坦克，利用其机动性避开敌方战斗机的攻击，转而集中力量打击敌方地面目标。"
  }
}
``` 

此分析综合了敌我双方的优劣势、关键威胁与机会，提供了具体的战术建议和作战方案，并结合了地形、天气等因素，确保作战计划的可行性和灵活性。
2025-09-22 10:47:49,741 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 10:47:49,742 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 10:47:49,742 - main_fastapi - INFO - 向 23 个客户端广播消息
2025-09-22 10:47:49,743 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,743 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,744 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 10:47:49,744 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,744 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,745 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 10:47:49,745 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,746 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,746 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,746 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,746 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 22
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 21
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 20
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 19
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 18
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 17
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 16
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 15
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 14
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 13
2025-09-22 10:47:49,746 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 12
2025-09-22 10:47:49,746 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758509239028
2025-09-22 10:47:49,746 - main_fastapi - INFO - ================================================================================
2025-09-22 10:47:49,746 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758509239028
2025-09-22 10:47:49,746 - main_fastapi - INFO - 总处理时间: 30713.84ms
2025-09-22 10:47:49,746 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 10:47:49,746 - main_fastapi - INFO - 消耗tokens: 2759
2025-09-22 10:47:49,746 - main_fastapi - INFO - ================================================================================
2025-09-22 10:47:49,747 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758509239028_20250922_104749.json
2025-09-22 10:48:24,438 - main_fastapi - INFO - ================================================================================
2025-09-22 10:48:24,438 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758509304438
2025-09-22 10:48:24,438 - main_fastapi - INFO - 模拟时间: 120.0分钟
2025-09-22 10:48:24,438 - main_fastapi - INFO - ================================================================================
2025-09-22 10:48:24,438 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 10:48:24,438 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 10:48:24,438 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 116.90846554387853,
    "latitude": 39.991081710050416,
    "altitude": 45.2
  },
  "status": {
    "health": 90.03150116396401,
    "ammo": 71.89021242069762,
    "fuel": 39.44181687869604,
    "operational": true
  },
  "threat_level": "高",
  "confidence": 0.5172830310310808,
  "last_seen": "2025-09-22T10:48:24.170582",
  "speed": 25.0,
  "heading": 44.81477984426303
}
2025-09-22 10:48:24,438 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 117.00918234514971,
    "latitude": 39.535880994617315,
    "altitude": 52.1
  },
  "status": {
    "health": 100.0,
    "ammo": 38.75114192386902,
    "fuel": 33.259203285510715,
    "operational": true
  },
  "threat_level": "中",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:48:24.170632",
  "speed": 35.0,
  "heading": 116.27194669584637
}
2025-09-22 10:48:24,438 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 98.89076476454836,
    "latitude": 44.362360250702146,
    "altitude": 8901.270189142064
  },
  "status": {
    "health": 82.04980368343251,
    "ammo": 62.48672811956282,
    "fuel": 26.83384690129569,
    "operational": true
  },
  "threat_level": "极高",
  "confidence": 0.5003654402521646,
  "last_seen": "2025-09-22T10:48:24.170666",
  "speed": 800.0,
  "heading": 266.7101590877178
}
2025-09-22 10:48:24,439 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 10:48:24,439 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.96162883194191,
    "latitude": 40.28648304081316,
    "altitude": 48.5
  },
  "status": {
    "health": 59.92883712539533,
    "ammo": 78.80109652075157,
    "fuel": 41.17363750986349,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:48:24.170693",
  "speed": 30.0,
  "heading": 300.35608907986114
}
2025-09-22 10:48:24,439 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 111.44488773066229,
    "latitude": 24.867653055020956,
    "altitude": 9330.816140966703
  },
  "status": {
    "health": 98.46824335988109,
    "ammo": 82.21468180992971,
    "fuel": 36.007801077563364,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:48:24.170720",
  "speed": 900.0,
  "heading": 187.82581965774438
}
2025-09-22 10:48:24,439 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 10:48:24,439 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 10:48:24,768 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:48:24,768 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:48:24,770 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 10:48:24,770 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 331.38ms
2025-09-22 10:48:24,770 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:48:24,776 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 10:48:25,102 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:48:25,102 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:48:25,105 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 10:48:25,105 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 328.47ms
2025-09-22 10:48:25,105 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:48:25,110 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 10:48:25,447 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:48:25,447 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:48:25,449 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 10:48:25,449 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 338.94ms
2025-09-22 10:48:25,449 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 10:48:25,454 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 10:48:25,784 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:48:25,784 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:48:25,786 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 10:48:25,786 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 332.02ms
2025-09-22 10:48:25,786 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 10:48:25,791 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 10:48:26,119 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:48:26,119 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:48:26,121 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 10:48:26,121 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 330.15ms
2025-09-22 10:48:26,122 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:48:26,126 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 10:48:26,128 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 10:48:26,128 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 10:48:26,128 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 10:48:26,128 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 10:48:26,128 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.24ms
2025-09-22 10:48:26,128 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 10:48:26,128 - main_fastapi - INFO - 用户提示词长度: 2072 字符
2025-09-22 10:48:26,128 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 10:48:26,128 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 10:48:26,128 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 10:48:26,128 - main_fastapi - INFO - LLM服务配置:
2025-09-22 10:48:26,128 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 10:48:26,129 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 10:48:26,129 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 10:48:26,129 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 10:48:26,129 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 10:48:26,129 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 10:48:26,129 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度116.90846554387853, 纬度39.991081710050416, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5172830310310808

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度117.00918234514971, 纬度39.535880994617315, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度98.89076476454836, 纬度44.362360250702146, 高度8901.270189142064m
- 威胁等级: 极高
- 置信度: 0.5003654402521646


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.96162883194191, 纬度40.28648304081316, 高度48.5m
- 状态: 可作战
- 健康度: 59.92883712539533%
- 弹药状态: 78.80109652075157%
- 燃料状态: 41.17363750986349%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度111.44488773066229, 纬度24.867653055020956, 高度9330.816140966703m
- 状态: 可作战
- 健康度: 98.46824335988109%
- 弹药状态: 82.21468180992971%
- 燃料状态: 36.007801077563364%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 10:48:26,129 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 10:48:26,129 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 10:48:26,129 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 10:49:04,059 - src.llm_service - INFO - LLM分析完成 - 耗时: 37929.94ms, 消耗tokens: 3093
2025-09-22 10:49:04,059 - main_fastapi - INFO - 大模型分析成功 - 耗时: 37930.63ms
2025-09-22 10:49:04,059 - main_fastapi - INFO - 消耗tokens: 3093
2025-09-22 10:49:04,059 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 10:49:04,059 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 10:49:04,059 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机（极高的空中威胁，具备强大的对地和对空作战能力）以及T-90主战坦克（强大的地面火力与防护，对我方地面部队构成直接威胁）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": ["T-90主战坦克的火力和防护能力优秀，适合集群作战；苏-35战斗机的多用途作战能力，包括对地攻击和制空权争夺；BTR-80装甲运兵车的高机动性和两栖能力，适合快速部署步兵"],
    "我方优势": ["歼-20战斗机的隐身能力和超音速巡航性能，适合实施远程打击和空中突袭；99A主战坦克的火力和防护性能接近T-90，且机动性稍强"],
    "关键弱点": ["我方地面部队缺乏足够的空中掩护，容易受到敌方战机的打击；敌方战机的威胁等级极高，可能干扰我方地面行动；地形平坦，不利于隐蔽机动"]
  },
  "战术建议": {
    "推荐策略": "联合防空与地面进攻相结合，优先夺取制空权，然后集中地面部队打击敌方装甲目标",
    "优先目标": "苏-35战斗机（首要目标，必须尽早削弱其空中威胁）和T-90主战坦克（地面主要威胁）",
    "兵力部署": "歼-20战斗机应迅速升空，建立空中优势，同时为地面部队提供掩护；99A主战坦克应利用地形隐蔽前进，寻找合适的伏击点或集结点，避免正面冲突",
    "注意事项": ["注意保持通信畅通，确保空中与地面部队的信息共享；警惕敌方战机的低空突袭；合理分配弹药和燃料，确保持续作战能力；避免暴露行踪，充分利用地形进行隐蔽机动"]
  },
  "作战方案": [
    {
      "方案名称": "方案一：空中先发制人，地面集中突破",
      "执行步骤": [
        "第一步：歼-20战斗机立即升空，利用隐身性能迅速搜索并锁定苏-35战斗机，实施超视距攻击。",
        "第二步：在空中威胁被削弱后，99A主战坦克向T-90主战坦克所在区域推进，利用地形隐蔽，逐步靠近并准备伏击。",
        "第三步：一旦时机成熟，99A主战坦克发动集中火力突击，配合精确打击，摧毁T-90主战坦克。",
        "第四步：歼-20战斗机继续监控空域，防止敌方战机反击或增援"
      ],
      "成功概率": "70%",
      "风险评估": "如果歼-20未能有效削弱敌方空中威胁，地面部队将面临较大风险；T-90主战坦克的火力和防护可能导致地面战斗时间延长，增加损耗"
    },
    {
      "方案名称": "方案二：分散诱敌，逐个击破",
      "执行步骤": [
        "第一步：派出少量地面部队（如轻型装甲车或步兵）佯攻BTR-80装甲运兵车，吸引敌方注意力。",
        "第二步：歼-20战斗机趁机攻击苏-35战斗机，利用敌方分心的机会夺取制空权。",
        "第三步：主力部队（99A主战坦克）绕过敌方主防线，从侧翼或后方包抄T-90主战坦克。",
        "第四步：歼-20战斗机继续监控并清理残余空中威胁，确保地面部队安全"
      ],
      "成功概率": "65%",
      "风险评估": "分散兵力可能导致个别小队被敌方集中火力消灭；敌方可能会识破诱饵，导致计划失败"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地形的开阔性，选择远离敌方主要阵地的方向快速撤离，优先利用机动性强的车辆（如99A主战坦克）拖曳受损装备。",
    "支援需求": "请求空中加油机支援，确保歼-20战斗机的续航能力；派遣无人机进行侦察，实时监控敌方动向；请求地面反导系统或防空导弹支援，应对敌方战机威胁。",
    "备用方案": "若制空权无法有效掌握，地面部队应转入防御姿态，利用地形建立临时防御阵地，等待后续支援"
  }
}
``` 

### 分析说明：
1. **威胁评估**：
   - 敌方的主要威胁来自苏-35战斗机和T-90主战坦克，它们分别在空中和地面构成了极大的威胁。
   - 敌方的BTR-80虽然威胁等级较低，但其高机动性和两栖能力仍需引起重视。

2. **力量对比**：
   - 敌方的优势在于其综合化的作战能力，尤其是苏-35和T-90的协同作战潜力。
   - 我方的优势在于歼-20的隐身和超音速性能，以及99A主战坦克的强大火力和防护能力。
   - 关键弱点在于地面部队缺乏空中掩护，而敌方战机的威胁等级极高。

3. **战术建议**：
   - 推荐的策略是首先通过空中打击削弱敌方空中威胁，随后集中地面力量打击敌方装甲目标。
   - 优先目标是敌方的制空力量（苏-35）和地面核心力量（T-90），以减少对我方的威胁。

4. **作战方案**：
   - 方案一侧重于快速夺取制空权，然后集中地面力量突破敌方防御。
   - 方案二则通过分散诱敌，逐个击破敌方力量，降低一次性损失的风险。

5. **应急预案**：
   - 撤退路线的选择应尽量远离敌方主要阵地，利用平原地形的开阔性进行快速转移。
   - 支援需求包括空中加油、无人机侦察和地面防空系统的支持，以增强我方综合作战能力。
   - 备用方案强调在不利情况下转入防御姿态，等待后续支援。

此分析综合考虑了敌我双方的优劣势、环境因素及可能的风险，提供了切实可行的战术建议和作战方案。
2025-09-22 10:49:04,059 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 10:49:04,059 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 10:49:04,060 - main_fastapi - INFO - 向 12 个客户端广播消息
2025-09-22 10:49:04,062 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758509304438
2025-09-22 10:49:04,062 - main_fastapi - INFO - ================================================================================
2025-09-22 10:49:04,062 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758509304438
2025-09-22 10:49:04,062 - main_fastapi - INFO - 总处理时间: 39621.68ms
2025-09-22 10:49:04,063 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 10:49:04,063 - main_fastapi - INFO - 消耗tokens: 3093
2025-09-22 10:49:04,063 - main_fastapi - INFO - ================================================================================
2025-09-22 10:49:04,063 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758509304438_20250922_104904.json
2025-09-22 10:49:29,962 - main_fastapi - INFO - ================================================================================
2025-09-22 10:49:29,962 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758509369962
2025-09-22 10:49:29,962 - main_fastapi - INFO - 模拟时间: 180.0分钟
2025-09-22 10:49:29,962 - main_fastapi - INFO - ================================================================================
2025-09-22 10:49:29,962 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 10:49:29,962 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 10:49:29,962 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 116.95085541393851,
    "latitude": 40.18116702878288,
    "altitude": 45.2
  },
  "status": {
    "health": 88.56223831693264,
    "ammo": 56.52758472681568,
    "fuel": 15.598488930317286,
    "operational": true
  },
  "threat_level": "高",
  "confidence": 0.5058800246966803,
  "last_seen": "2025-09-22T10:49:29.690775",
  "speed": 25.0,
  "heading": 300.3546104708012
}
2025-09-22 10:49:29,962 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 117.40535450989945,
    "latitude": 39.50349431345361,
    "altitude": 52.1
  },
  "status": {
    "health": 100.0,
    "ammo": 16.07756939317484,
    "fuel": 8.877754486140795,
    "operational": true
  },
  "threat_level": "中",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:49:29.690821",
  "speed": 35.0,
  "heading": 124.44003647615955
}
2025-09-22 10:49:29,962 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 92.5176809128536,
    "latitude": 41.06831287655052,
    "altitude": 8647.976756587099
  },
  "status": {
    "health": 77.81314601326997,
    "ammo": 53.19942617726917,
    "fuel": 3.807206908921033,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5062739535098391,
  "last_seen": "2025-09-22T10:49:19.877978",
  "speed": 800.0,
  "heading": 207.37924848496274
}
2025-09-22 10:49:29,962 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 10:49:29,962 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.62436996197032,
    "latitude": 40.348348526548264,
    "altitude": 48.5
  },
  "status": {
    "health": 50.536266217159906,
    "ammo": 65.91702861987562,
    "fuel": 15.621782945825753,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:49:29.690852",
  "speed": 30.0,
  "heading": 291.89598101493067
}
2025-09-22 10:49:29,963 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 108.56441795894304,
    "latitude": 17.184367509374976,
    "altitude": 8849.663126335654
  },
  "status": {
    "health": 89.90772583464145,
    "ammo": 79.96170533486193,
    "fuel": 10.165928354857257,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:49:29.690882",
  "speed": 900.0,
  "heading": 155.47219119262442
}
2025-09-22 10:49:29,963 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 10:49:29,963 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 10:49:30,297 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:49:30,298 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:49:30,301 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 10:49:30,301 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 338.51ms
2025-09-22 10:49:30,301 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:49:30,311 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 10:49:30,643 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:49:30,644 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:49:30,649 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 10:49:30,649 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 338.17ms
2025-09-22 10:49:30,649 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:49:30,654 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 10:49:30,984 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:49:30,984 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:49:30,987 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 10:49:30,987 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 333.05ms
2025-09-22 10:49:30,987 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 10:49:30,991 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 10:49:31,320 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:49:31,320 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:49:31,322 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 10:49:31,322 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 330.58ms
2025-09-22 10:49:31,322 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 10:49:31,328 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 10:49:31,658 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:49:31,659 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:49:31,661 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 10:49:31,661 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 332.66ms
2025-09-22 10:49:31,661 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:49:31,667 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 10:49:31,669 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 10:49:31,669 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 10:49:31,669 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 10:49:31,669 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 10:49:31,669 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.36ms
2025-09-22 10:49:31,669 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 10:49:31,669 - main_fastapi - INFO - 用户提示词长度: 2071 字符
2025-09-22 10:49:31,669 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 10:49:31,669 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 10:49:31,670 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 10:49:31,670 - main_fastapi - INFO - LLM服务配置:
2025-09-22 10:49:31,670 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 10:49:31,670 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 10:49:31,670 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 10:49:31,670 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 10:49:31,670 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 10:49:31,670 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 10:49:31,670 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度116.95085541393851, 纬度40.18116702878288, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5058800246966803

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度117.40535450989945, 纬度39.50349431345361, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度92.5176809128536, 纬度41.06831287655052, 高度8647.976756587099m
- 威胁等级: 极高
- 置信度: 0.5062739535098391


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.62436996197032, 纬度40.348348526548264, 高度48.5m
- 状态: 可作战
- 健康度: 50.536266217159906%
- 弹药状态: 65.91702861987562%
- 燃料状态: 15.621782945825753%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度108.56441795894304, 纬度17.184367509374976, 高度8849.663126335654m
- 状态: 可作战
- 健康度: 89.90772583464145%
- 弹药状态: 79.96170533486193%
- 燃料状态: 10.165928354857257%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 10:49:31,670 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 10:49:31,670 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 10:49:31,670 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 10:50:03,749 - src.llm_service - INFO - LLM分析完成 - 耗时: 32078.88ms, 消耗tokens: 2875
2025-09-22 10:50:03,749 - main_fastapi - INFO - 大模型分析成功 - 耗时: 32079.58ms
2025-09-22 10:50:03,749 - main_fastapi - INFO - 消耗tokens: 2875
2025-09-22 10:50:03,749 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 10:50:03,749 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 10:50:03,749 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "苏-35战斗机（空中威胁）与T-90主战坦克（地面威胁）构成双重危险，尤其是苏-35的空中打击能力对地面部队构成极大威胁。",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "空中优势：拥有苏-35战斗机，具备强大的空中打击能力。",
      "地面火力：T-90主战坦克火力与防护性能优异，适合集群作战。",
      "地形适应性：BTR-80装甲车机动性强，适合平原地形作战。"
    ],
    "我方优势": [
      "隐身技术：歼-20战斗机具有出色的隐身能力和超音速巡航能力。",
      "地面火力：99A主战坦克火力强大，防护性能优秀，且具备先进的信息化作战能力。",
      "高度灵活性：歼-20在空战中机动性极强，可迅速应对突发情况。"
    ],
    "关键弱点": [
      "燃料不足：我方99A主战坦克燃料状态较低，限制了其持续作战能力。",
      "空中防御：我方缺乏有效的防空网络，难以完全抵御敌方空中打击。",
      "地形利用：平原地形不利于隐蔽，增加了我方被发现的风险。"
    ]
  },
  "战术建议": {
    "推荐策略": "采取联合防空与地面突击相结合的策略，优先削弱敌方空中优势，随后集中地面力量突破敌方防御。",
    "优先目标": "苏-35战斗机（优先摧毁空中威胁），其次为T-90主战坦克（地面主要威胁）。",
    "兵力部署": "歼-20负责空中作战，优先锁定并摧毁敌方苏-35；99A主战坦克负责地面推进，配合歼-20行动，避免直接暴露于敌方空中火力下。",
    "注意事项": [
      "保持通信畅通，确保空中与地面部队协同作战。",
      "注意燃料管理，合理分配99A主战坦克的行动范围。",
      "利用晴朗天气条件，发挥我方隐身与火力优势，同时注意敌方可能的电子干扰。"
    ]
  },
  "作战方案": [
    {
      "方案名称": "空中压制与地面突破",
      "执行步骤": [
        "步骤1：歼-20迅速进入作战区域，利用隐身能力接近敌方苏-35，采用超视距攻击模式进行首轮打击。",
        "步骤2：99A主战坦克在地面待命，利用地形掩护，准备应对可能的敌方地面反击。",
        "步骤3：歼-20确认敌方空中威胁降低后，引导99A主战坦克发起地面突击，重点打击T-90主战坦克。"
      ],
      "成功概率": "60%",
      "风险评估": "歼-20可能遭遇敌方电子干扰或防空火力，导致空中压制效果减弱；99A主战坦克在缺乏空中掩护的情况下，容易受到敌方空中打击。"
    },
    {
      "方案名称": "诱饵战术与集中火力",
      "执行步骤": [
        "步骤1：使用无人机或其他诱饵吸引敌方苏-35注意力，分散其火力。",
        "步骤2：歼-20从不同方向发动突袭，利用隐身优势对苏-35进行饱和式打击。",
        "步骤3：99A主战坦克快速机动至敌方T-90后方，利用地形掩护实施伏击。"
      ],
      "成功概率": "55%",
      "风险评估": "诱饵战术可能被敌方识破，导致歼-20陷入不利局面；99A主战坦克机动过程中可能暴露于敌方空中火力之下。"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地形开阔的特点，规划多条撤退路线，确保99A主战坦克能够迅速脱离战场，同时歼-20提供空中掩护。",
    "支援需求": "请求地面防空力量支援，增强对敌方空中威胁的应对能力；补充99A主战坦克的燃料供应。",
    "备用方案": "如空中压制失败，99A主战坦克应立即转入防御姿态，寻找有利地形设伏，等待后续支援力量到位。"
  }
}
``` 

### 分析说明：
1. **威胁评估**：敌方的主要威胁来自苏-35战斗机和T-90主战坦克，其中苏-35的空中打击能力对我方地面部队构成最大威胁。
2. **力量对比**：敌方在空中和地面均有一定优势，但我方歼-20的隐身和机动性可以有效对抗敌方空中威胁，而99A主战坦克则能在地面作战中发挥重要作用。
3. **战术建议**：优先解决敌方空中威胁，再集中地面力量突破敌方防线。同时，注意燃料管理和通信保障。
4. **作战方案**：提供了两个主要方案，分别是空中压制与地面突破、诱饵战术与集中火力，每个方案都针对不同的风险作了详细说明。
5. **应急预案**：确保在作战不利时有撤退路线和支援需求，同时制定了备用方案以应对突发情况。
2025-09-22 10:50:03,749 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 10:50:03,749 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 10:50:03,750 - main_fastapi - INFO - 向 12 个客户端广播消息
2025-09-22 10:50:03,754 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758509369962
2025-09-22 10:50:03,754 - main_fastapi - INFO - ================================================================================
2025-09-22 10:50:03,754 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758509369962
2025-09-22 10:50:03,754 - main_fastapi - INFO - 总处理时间: 33787.53ms
2025-09-22 10:50:03,754 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 10:50:03,754 - main_fastapi - INFO - 消耗tokens: 2875
2025-09-22 10:50:03,754 - main_fastapi - INFO - ================================================================================
2025-09-22 10:50:03,756 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758509369962_20250922_105003.json
2025-09-22 10:50:35,528 - main_fastapi - INFO - ================================================================================
2025-09-22 10:50:35,528 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758509435528
2025-09-22 10:50:35,528 - main_fastapi - INFO - 模拟时间: 240.0分钟
2025-09-22 10:50:35,528 - main_fastapi - INFO - ================================================================================
2025-09-22 10:50:35,528 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 10:50:35,528 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 10:50:35,528 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 116.8874664718138,
    "latitude": 40.27561369877946,
    "altitude": 45.2
  },
  "status": {
    "health": 84.68263243979167,
    "ammo": 43.84423135260813,
    "fuel": 4.183515711751653,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:50:05.738851",
  "speed": 25.0,
  "heading": 25.10826187208805
}
2025-09-22 10:50:35,528 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 117.45630905106732,
    "latitude": 39.47667481819249,
    "altitude": 52.1
  },
  "status": {
    "health": 100.0,
    "ammo": 16.07756939317484,
    "fuel": 4.8197844668431635,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.506318577949467,
  "last_seen": "2025-09-22T10:49:39.524758",
  "speed": 35.0,
  "heading": 94.53610057363034
}
2025-09-22 10:50:35,529 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 92.5176809128536,
    "latitude": 41.06831287655052,
    "altitude": 8647.976756587099
  },
  "status": {
    "health": 77.81314601326997,
    "ammo": 53.19942617726917,
    "fuel": 3.807206908921033,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5062739535098391,
  "last_seen": "2025-09-22T10:49:19.877978",
  "speed": 800.0,
  "heading": 207.37924848496274
}
2025-09-22 10:50:35,529 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 10:50:35,529 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.58605516881397,
    "latitude": 40.451883560635544,
    "altitude": 48.5
  },
  "status": {
    "health": 47.31369800083677,
    "ammo": 62.52896688501232,
    "fuel": 4.669642811137721,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:49:59.193253",
  "speed": 30.0,
  "heading": 358.8293465091557
}
2025-09-22 10:50:35,529 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 110.26435614473485,
    "latitude": 15.196872274762313,
    "altitude": 8693.781387052832
  },
  "status": {
    "health": 89.90772583464145,
    "ammo": 79.96170533486193,
    "fuel": 3.8985161256782748,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:49:49.364240",
  "speed": 900.0,
  "heading": 102.10759100066413
}
2025-09-22 10:50:35,529 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 10:50:35,529 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 10:50:35,856 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:50:35,856 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:50:35,859 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 10:50:35,859 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 329.77ms
2025-09-22 10:50:35,859 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:50:35,864 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 10:50:36,190 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:50:36,190 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:50:36,193 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 10:50:36,193 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 328.94ms
2025-09-22 10:50:36,193 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:50:36,197 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 10:50:36,530 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:50:36,530 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:50:36,532 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 10:50:36,532 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 335.18ms
2025-09-22 10:50:36,532 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 10:50:36,538 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 10:50:36,872 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:50:36,872 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:50:36,874 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 10:50:36,874 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 336.92ms
2025-09-22 10:50:36,875 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 10:50:36,881 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 10:50:37,210 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 10:50:37,210 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 10:50:37,213 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 10:50:37,213 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 332.02ms
2025-09-22 10:50:37,213 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 10:50:37,222 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 10:50:37,222 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 10:50:37,222 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 10:50:37,222 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 10:50:37,222 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 10:50:37,222 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.23ms
2025-09-22 10:50:37,222 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 10:50:37,222 - main_fastapi - INFO - 用户提示词长度: 2069 字符
2025-09-22 10:50:37,222 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 10:50:37,222 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 10:50:37,222 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 10:50:37,222 - main_fastapi - INFO - LLM服务配置:
2025-09-22 10:50:37,222 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 10:50:37,222 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 10:50:37,222 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 10:50:37,222 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 10:50:37,223 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 10:50:37,223 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 10:50:37,223 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度116.8874664718138, 纬度40.27561369877946, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度117.45630905106732, 纬度39.47667481819249, 高度52.1m
- 威胁等级: 中
- 置信度: 0.506318577949467

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度92.5176809128536, 纬度41.06831287655052, 高度8647.976756587099m
- 威胁等级: 极高
- 置信度: 0.5062739535098391


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.58605516881397, 纬度40.451883560635544, 高度48.5m
- 状态: 不可作战
- 健康度: 47.31369800083677%
- 弹药状态: 62.52896688501232%
- 燃料状态: 4.669642811137721%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度110.26435614473485, 纬度15.196872274762313, 高度8693.781387052832m
- 状态: 不可作战
- 健康度: 89.90772583464145%
- 弹药状态: 79.96170533486193%
- 燃料状态: 3.8985161256782748%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 10:50:37,223 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 10:50:37,223 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 10:50:37,223 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 10:51:08,931 - src.llm_service - INFO - LLM分析完成 - 耗时: 31708.49ms, 消耗tokens: 2857
2025-09-22 10:51:08,931 - main_fastapi - INFO - 大模型分析成功 - 耗时: 31709.15ms
2025-09-22 10:51:08,931 - main_fastapi - INFO - 消耗tokens: 2857
2025-09-22 10:51:08,931 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 10:51:08,932 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 10:51:08,932 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机，具备空中优势，对我方地面部队构成极大威胁；T-90主战坦克，火力与防护突出，对我方地面力量形成直接压力。",
    "威胁排序": [
      "苏-35战斗机",
      "T-90主战坦克",
      "BTR-80装甲运兵车"
    ]
  },
  "力量对比": {
    "敌方优势": [
      "苏-35战斗机在空中占据绝对优势，能够快速压制我方地面部队。",
      "T-90主战坦克拥有强大的火力与防护能力，适合平原地区的正面作战。",
      "BTR-80装甲运兵车机动性强，能快速支援其他作战单位。"
    ],
    "我方优势": [
      "歼-20战斗机虽然目前不可作战，但其隐身能力和先进航电系统在未来具有潜力。",
      "99A主战坦克虽然健康度较低，但在恢复状态下具备与敌方坦克抗衡的能力。"
    ],
    "关键弱点": [
      "我方地面部队（99A主战坦克）目前处于不可作战状态，无法有效应对敌方威胁。",
      "我方空中力量（歼-20战斗机）同样不可作战，无法对敌方空中力量进行有效反击。",
      "地形开阔，不利于我方隐蔽和机动。"
    ]
  },
  "战术建议": {
    "推荐策略": "先稳后攻，优先恢复我方地面和空中力量，再采取针对性行动。",
    "优先目标": "苏-35战斗机，必须优先压制或诱离敌方空中力量，为后续地面作战创造条件。",
    "兵力部署": "在我方恢复战斗力之前，集中力量建立防御阵地，利用地形和现有资源构建多层次防御体系，避免与敌方正面冲突。",
    "注意事项": [
      "密切关注敌方动向，特别是苏-35战斗机的动态。",
      "尽量避免暴露地面部队，利用地形和伪装减少被发现的可能性。",
      "确保后勤补给线畅通，尽快修复我方单位，提升战斗力。"
    ]
  },
  "作战方案": [
    {
      "方案名称": "防空优先，诱敌深入",
      "执行步骤": [
        "利用现有防空设施和雷达系统，尝试诱骗苏-35战斗机进入我方防空火力范围。",
        "地面部队依托有利地形，建立多层防御阵地，避免直接暴露于敌方T-90坦克的火力下。",
        "一旦苏-35战斗机被诱入防空圈，立即启动防空系统进行拦截。",
        "同时，集中地面力量对付BTR-80装甲运兵车，防止其渗透或支援T-90坦克。"
      ],
      "成功概率": "30%",
      "风险评估": "该方案依赖于精准的情报和诱骗操作，失败可能导致我方地面部队过早暴露，遭受敌方空中和地面双重打击。"
    },
    {
      "方案名称": "被动防御，等待援军",
      "执行步骤": [
        "构筑坚固的防御阵地，利用地形优势设置陷阱，如反坦克雷区和伏击点。",
        "保持无线电静默，避免敌方精确定位。",
        "等待我方增援力量（如维修团队、空中支援）抵达，恢复战斗力后再发起反击。",
        "同时派遣侦察无人机或小队，持续监控敌方动向。"
      ],
      "成功概率": "60%",
      "风险评估": "该方案较为保守，可能错失主动出击的机会，但能最大限度保存实力，为后续反击奠定基础。"
    }
  ],
  "应急预案": {
    "撤退路线": "利用现有交通线路，选择远离敌方主要威胁的方向撤退，优先保障人员安全。",
    "支援需求": "急需维修团队修复99A主战坦克和歼-20战斗机，同时请求空中支援（如预警机或远程轰炸机）协助压制敌方空中力量。",
    "备用方案": "若无法修复地面和空中力量，考虑使用远程火炮或无人机对敌方关键目标进行骚扰，争取时间等待增援。"
  }
}
``` 

### 解析说明：
1. **威胁评估**：根据敌方单位的性能和威胁等级，苏-35战斗机因其极高的威胁等级成为最大威胁，其次是T-90主战坦克。
   
2. **力量对比**：敌方在空中和地面均占有明显优势，而我方因主要单位不可作战，处于劣势地位，但仍有潜在优势（如歼-20的隐身能力、99A的潜在战斗力）。

3. **战术建议**：鉴于我方当前状况，优先目标是苏-35战斗机，通过诱骗或防御手段削弱敌方空中优势，为后续地面作战创造条件。

4. **作战方案**：提供了两种方案，一种是积极的“防空优先，诱敌深入”，另一种是保守的“被动防御，等待援军”。前者风险较高但可能获得主动权，后者更为稳妥但可能错失战机。

5. **应急预案**：考虑到最坏情况，制定了撤退路线和支援需求，确保即使在不利局势下也能最大程度保存实力。

此分析综合考虑了敌我双方的实力对比、战场环境和战术可行性，旨在为指挥官提供全面的决策支持。
2025-09-22 10:51:08,932 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 10:51:08,932 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 10:51:08,932 - main_fastapi - INFO - 向 12 个客户端广播消息
2025-09-22 10:51:08,935 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758509435528
2025-09-22 10:51:08,935 - main_fastapi - INFO - ================================================================================
2025-09-22 10:51:08,935 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758509435528
2025-09-22 10:51:08,935 - main_fastapi - INFO - 总处理时间: 33403.9ms
2025-09-22 10:51:08,935 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 10:51:08,936 - main_fastapi - INFO - 消耗tokens: 2857
2025-09-22 10:51:08,936 - main_fastapi - INFO - ================================================================================
2025-09-22 10:51:08,936 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758509435528_20250922_105108.json
