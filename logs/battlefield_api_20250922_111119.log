2025-09-22 11:11:19,215 - __main__ - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250922_111119.log
2025-09-22 11:11:19,647 - __mp_main__ - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250922_111119.log
2025-09-22 11:11:19,706 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:19,900 - main_fastapi - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250922_111119.log
2025-09-22 11:11:19,900 - main_fastapi - INFO - 战场态势分析API服务启动
2025-09-22 11:11:20,057 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:20,387 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 1
2025-09-22 11:11:20,407 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:20,757 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:21,108 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:21,458 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:21,757 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 2
2025-09-22 11:11:21,787 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 3
2025-09-22 11:11:21,809 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:21,809 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 4
2025-09-22 11:11:21,839 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 5
2025-09-22 11:11:21,872 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 6
2025-09-22 11:11:21,896 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 7
2025-09-22 11:11:21,920 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 8
2025-09-22 11:11:21,952 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 9
2025-09-22 11:11:21,983 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 10
2025-09-22 11:11:22,010 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 11
2025-09-22 11:11:22,032 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 12
2025-09-22 11:11:22,062 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 13
2025-09-22 11:11:22,088 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 14
2025-09-22 11:11:22,118 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 15
2025-09-22 11:11:22,150 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 16
2025-09-22 11:11:22,159 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:22,172 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 17
2025-09-22 11:11:22,509 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:22,860 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:23,210 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:23,560 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:23,911 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:24,261 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:24,611 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:24,739 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 18
2025-09-22 11:11:24,769 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 19
2025-09-22 11:11:24,962 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:25,312 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:25,663 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:26,013 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:26,363 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:26,764 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:27,114 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:27,464 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:27,815 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:28,165 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:28,516 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:28,866 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:29,216 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:29,567 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:29,917 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:30,267 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:30,618 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:30,968 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:31,318 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:31,669 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:32,019 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:32,369 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:32,720 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:33,070 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:33,420 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:33,771 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:34,121 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:34,471 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:34,822 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:35,172 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:35,522 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:35,873 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:36,223 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:36,573 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:36,924 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:37,274 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:37,477 - main_fastapi - INFO - ================================================================================
2025-09-22 11:11:37,478 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758510697477
2025-09-22 11:11:37,478 - main_fastapi - INFO - 模拟时间: 960.0分钟
2025-09-22 11:11:37,478 - main_fastapi - INFO - ================================================================================
2025-09-22 11:11:37,478 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:11:37,478 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:11:37,478 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:11:37,478 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:11:37,478 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:11:37,478 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:11:37,478 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:11:37,478 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:11:37,478 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:11:37,478 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:11:37,554 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:11:37,554 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:11:37,557 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:11:37,557 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 78.45ms
2025-09-22 11:11:37,557 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:11:37,557 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:11:37,606 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:11:37,606 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:11:37,608 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:11:37,609 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.41ms
2025-09-22 11:11:37,609 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:11:37,609 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:11:37,624 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:37,661 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:11:37,661 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:11:37,663 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:11:37,663 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 54.2ms
2025-09-22 11:11:37,663 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:11:37,663 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:11:37,713 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:11:37,713 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:11:37,715 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:11:37,716 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 52.06ms
2025-09-22 11:11:37,716 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:11:37,716 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:11:37,764 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:11:37,764 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:11:37,767 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:11:37,767 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.15ms
2025-09-22 11:11:37,767 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:11:37,767 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:11:37,767 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:11:37,768 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:11:37,768 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:11:37,768 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:11:37,768 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.53ms
2025-09-22 11:11:37,768 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:11:37,768 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:11:37,768 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:11:37,768 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:11:37,818 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:11:37,818 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:11:37,818 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:11:37,818 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:11:37,818 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:11:37,818 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:11:37,818 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:11:37,818 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:11:37,818 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:11:37,818 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:11:37,818 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:11:37,818 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:11:37,975 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:38,325 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:38,675 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:39,026 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:39,376 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:39,726 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:40,077 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:40,427 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:40,777 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:41,128 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:41,478 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:41,828 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:42,179 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:42,529 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:42,879 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:43,230 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:43,580 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:43,930 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:44,281 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:44,631 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:44,981 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:45,332 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:45,682 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:46,032 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:46,383 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:46,733 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:47,083 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:47,433 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:47,784 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:48,134 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:48,485 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:48,835 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:49,185 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:49,536 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:49,886 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:50,236 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:50,587 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:50,937 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:51,287 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:51,638 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:51,988 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:52,338 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:52,689 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:53,039 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:53,389 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:53,739 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:54,090 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:54,490 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:54,841 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:55,191 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:55,541 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:55,891 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:56,242 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:56,592 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:56,942 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:57,293 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:57,643 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:57,993 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:58,344 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:11:58,694 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:59,045 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:59,395 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:11:59,745 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:00,095 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:00,446 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:00,796 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:01,147 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:01,497 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:01,847 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:02,198 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:02,548 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:02,898 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:03,249 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:03,599 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:03,950 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:04,300 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:04,650 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:05,001 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:05,351 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:05,701 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:06,052 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:06,402 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:06,752 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:07,103 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:07,453 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:07,803 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:08,154 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:08,504 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:08,855 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:09,205 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:09,555 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:09,575 - src.llm_service - INFO - LLM分析完成 - 耗时: 31756.29ms, 消耗tokens: 2868
2025-09-22 11:12:09,575 - main_fastapi - INFO - 大模型分析成功 - 耗时: 31806.57ms
2025-09-22 11:12:09,575 - main_fastapi - INFO - 消耗tokens: 2868
2025-09-22 11:12:09,575 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:12:09,575 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:12:09,575 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "苏-35战斗机（空中威胁）和T-90主战坦克（地面威胁）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "苏-35战斗机具备极强的空中作战能力，威胁极大",
      "T-90主战坦克火力强大且防护优秀，难以直接摧毁",
      "BTR-80装甲运兵车机动性强，可以快速支援和输送步兵"
    ],
    "我方优势": [
      "歼-20战斗机拥有隐身能力和超音速巡航能力，理论上具备制空权潜力",
      "99A主战坦克在性能上与T-90相当，但目前处于不可作战状态"
    ],
    "关键弱点": [
      "我方所有单位均处于不可作战状态，无法有效应对当前威胁",
      "缺乏地面防空能力，无法有效抵御苏-35战斗机的空中打击",
      "地形开阔，不利于隐蔽和机动"
    ]
  },
  "战术建议": {
    "推荐策略": "立即恢复我方单位战备状态，建立多层次防御体系，优先击落苏-35战斗机，再集中火力对付T-90主战坦克",
    "优先目标": "苏-35战斗机",
    "兵力部署": "优先部署防空力量（如便携式防空导弹或固定防空系统），集中火力针对苏-35战斗机；同时派遣侦察部队监控T-90主战坦克动向，为后续行动做准备。",
    "注意事项": [
      "确保指挥通信畅通，避免因敌方空中打击导致指挥中断",
      "迅速调动后方资源，修复我方单位，争取尽快投入战斗",
      "利用地形和伪装手段，尽可能隐藏重要目标，减少暴露"
    ]
  },
  "作战方案": [
    {
      "方案名称": "空中压制与地面反击",
      "执行步骤": [
        "紧急部署便携式防空导弹系统，针对苏-35战斗机进行拦截",
        "请求后方支援，尽快修复歼-20战斗机，争取夺取制空权",
        "利用无人机或其他侦察手段，精确锁定T-90主战坦克位置",
        "集中地面火力（如远程反坦克导弹），配合空中支援，逐步削弱T-90主战坦克战斗力"
      ],
      "成功概率": "60%",
      "风险评估": "如果苏-35战斗机持续对我方地面部队实施打击，可能导致地面部队损失严重，同时我方单位修复时间过长将影响整体作战能力"
    },
    {
      "方案名称": "诱敌深入与伏击",
      "执行步骤": [
        "制造假象，引导敌方T-90主战坦克进入预设伏击圈",
        "利用地形设置反坦克陷阱（如地雷、反坦克导弹阵地）",
        "待敌方进入伏击圈后，集中兵力进行突然袭击，力求迅速击毁T-90主战坦克",
        "同时使用电子干扰设备，扰乱敌方战斗机的雷达和通信系统，争取赢得短暂的时间窗口"
      ],
      "成功概率": "40%",
      "风险评估": "此方案风险较高，一旦诱敌失败，可能导致我方陷入被动局面，同时敌方战斗机可能提前发现伏击圈，对我方造成重大损失"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地形，选择多条撤离路线，分散撤退，避免被敌方集中火力打击",
    "支援需求": "急需地面防空支援（如防空导弹车队）、反坦克导弹车队以及快速维修团队",
    "备用方案": "若无法迅速修复我方单位，可考虑采取防御姿态，利用现有地形和工事建立坚固防线，等待增援"
  }
}
``` 

### 解析说明：
1. **威胁评估**：
   - 敌方的苏-35战斗机是最大的空中威胁，而T-90主战坦克则构成地面的主要威胁。
   - 敌方整体威胁等级为“极高”，因为其空中和地面力量都非常强大。

2. **力量对比**：
   - 敌方优势明显，尤其是空中力量和地面重型装备。
   - 我方虽然有潜在优势（如歼-20的隐身能力），但由于单位不可作战，目前处于劣势。
   - 关键弱点在于我方单位无法立即投入战斗，且缺乏有效的防空能力。

3. **战术建议**：
   - 首先解决空中威胁，通过部署防空系统拦截苏-35战斗机。
   - 优先修复我方单位，特别是歼-20战斗机，争取制空权。
   - 利用地形和侦察手段，逐步削弱敌方地面力量。

4. **作战方案**：
   - 方案一侧重于通过防空和地面火力结合，逐步削弱敌方力量，成功率较高但需要时间。
   - 方案二采取诱敌深入策略，风险较大但可能迅速取得成果。

5. **应急预案**：
   - 如果情况不利，需及时撤退并请求增援。
   - 支援需求包括防空力量、反坦克武器以及快速维修团队。

通过以上分析，可以制定出科学合理的作战计划，最大限度降低风险并提高胜算。
2025-09-22 11:12:09,575 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:12:09,575 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:12:09,575 - main_fastapi - INFO - 向 19 个客户端广播消息
2025-09-22 11:12:09,580 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758510697477
2025-09-22 11:12:09,580 - main_fastapi - INFO - ================================================================================
2025-09-22 11:12:09,580 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758510697477
2025-09-22 11:12:09,580 - main_fastapi - INFO - 总处理时间: 32097.62ms
2025-09-22 11:12:09,580 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:12:09,580 - main_fastapi - INFO - 消耗tokens: 2868
2025-09-22 11:12:09,580 - main_fastapi - INFO - ================================================================================
2025-09-22 11:12:09,581 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758510697477_20250922_111209.json
2025-09-22 11:12:09,906 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:10,256 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:10,606 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:10,957 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:11,307 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:11,657 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:12,007 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:12,358 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:12,708 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:13,058 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:13,409 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:13,759 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:14,110 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:14,460 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:14,810 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:15,161 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:15,511 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:15,861 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:16,212 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:16,562 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:16,912 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:17,263 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:17,613 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:17,963 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:18,313 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:18,664 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:19,014 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:19,365 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:19,715 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:20,065 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:20,416 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:20,766 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:21,116 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:21,467 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:21,817 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:22,218 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:22,568 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:22,918 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:23,269 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:23,619 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:23,969 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:24,320 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:24,670 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:25,020 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:25,371 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:25,721 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:26,072 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:26,422 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:26,772 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:27,123 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:27,473 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:27,823 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:28,174 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:28,524 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:28,874 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:29,225 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:29,575 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:29,925 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:30,276 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:30,626 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:30,976 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:31,327 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:31,677 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:32,028 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:32,378 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:32,728 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:33,079 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:33,429 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:33,779 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:34,130 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:34,480 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:34,830 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:35,181 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:35,531 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:35,881 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:36,232 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:36,582 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:36,933 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:37,283 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:37,633 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:37,984 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:38,334 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:38,684 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:39,035 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:39,385 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:39,735 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:40,086 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:40,436 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:40,786 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:41,137 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:41,487 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:41,837 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:42,188 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:42,538 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:42,888 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:43,239 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:43,589 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:43,940 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:44,290 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:44,640 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:44,991 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:45,341 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:45,691 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:46,042 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:46,442 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:46,791 - main_fastapi - INFO - ================================================================================
2025-09-22 11:12:46,792 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758510766791
2025-09-22 11:12:46,792 - main_fastapi - INFO - 模拟时间: 1020.0分钟
2025-09-22 11:12:46,792 - main_fastapi - INFO - ================================================================================
2025-09-22 11:12:46,792 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:12:46,792 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:12:46,792 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:12:46,792 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:12:46,792 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:12:46,792 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:12:46,792 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:12:46,792 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:12:46,792 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:12:46,792 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:12:46,792 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:46,845 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:12:46,845 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:12:46,847 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:12:46,848 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 55.29ms
2025-09-22 11:12:46,848 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:12:46,848 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:12:46,903 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:12:46,903 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:12:46,906 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:12:46,906 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 57.61ms
2025-09-22 11:12:46,906 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:12:46,906 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:12:46,959 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:12:46,959 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:12:46,961 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:12:46,961 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 55.02ms
2025-09-22 11:12:46,961 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:12:46,961 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:12:47,010 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:12:47,010 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:12:47,014 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:12:47,014 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 52.77ms
2025-09-22 11:12:47,014 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:12:47,015 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:12:47,067 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:12:47,067 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:12:47,070 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:12:47,070 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 54.97ms
2025-09-22 11:12:47,070 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:12:47,070 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:12:47,070 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:12:47,070 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:12:47,070 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:12:47,070 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:12:47,070 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.23ms
2025-09-22 11:12:47,070 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:12:47,070 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:12:47,070 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:12:47,070 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:12:47,071 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:12:47,071 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:12:47,071 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:12:47,071 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:12:47,071 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:12:47,071 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:12:47,071 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:12:47,071 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:12:47,071 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:12:47,071 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:12:47,071 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:12:47,071 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:12:47,143 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:47,493 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:47,843 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:48,194 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:48,544 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:48,894 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:49,245 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:49,595 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:49,946 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:50,296 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:50,646 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:50,997 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:51,347 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:51,697 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:52,048 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:52,398 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:52,748 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:53,099 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:53,449 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:53,800 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:54,150 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:54,500 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:54,851 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:55,201 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:55,551 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:55,902 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:56,252 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:56,602 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:56,952 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:57,303 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:12:57,653 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:58,003 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:58,354 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:58,704 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:59,054 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:59,405 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:12:59,755 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:00,105 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:00,456 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:00,806 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:01,156 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:01,507 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:01,857 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:02,207 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:02,558 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:02,908 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:03,259 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:03,609 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:03,959 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:04,310 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:04,660 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:05,010 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:05,360 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:05,711 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:06,061 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:06,411 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:06,762 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:07,112 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:07,462 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:07,813 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:08,163 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:08,513 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:08,864 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:09,214 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:09,564 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:09,915 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:10,265 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:10,615 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:10,966 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:11,316 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:11,666 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:12,017 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:12,367 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:12,717 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:13,067 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:13,418 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:13,768 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:14,118 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:14,469 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:14,819 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:15,170 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:15,520 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:15,870 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:16,221 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:16,571 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:16,921 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:17,272 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:17,622 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:17,972 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:18,323 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:18,673 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:19,023 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:19,374 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:19,724 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:20,074 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:20,196 - src.llm_service - INFO - LLM分析完成 - 耗时: 33124.79ms, 消耗tokens: 2919
2025-09-22 11:13:20,196 - main_fastapi - INFO - 大模型分析成功 - 耗时: 33125.41ms
2025-09-22 11:13:20,196 - main_fastapi - INFO - 消耗tokens: 2919
2025-09-22 11:13:20,196 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:13:20,196 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:13:20,196 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "苏-35战斗机（空中威胁）和T-90主战坦克（地面威胁）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "空中优势：苏-35战斗机具备强大的制空能力和对地支援能力。",
      "地面火力：T-90主战坦克火力强大，防护优秀，适合集群作战。",
      "地形利用：平原地形有利于机械化部队的快速机动。",
      "天气条件：晴朗天气有助于发挥空中和地面部队的性能。"
    ],
    "我方优势": [
      "歼-20战斗机具备隐身能力和超音速巡航能力，理论上可以有效打击敌方空中力量。",
      "99A主战坦克在技术参数上与T-90相当，但目前不可作战。",
      "天气和地形对我方同样有利，便于实施机动战术。"
    ],
    "关键弱点": [
      "我方主力单位均处于不可作战状态，缺乏有效的反击能力。",
      "缺乏地面防空能力，难以应对敌方空中威胁。",
      "我方没有足够的地面装甲力量来对抗T-90主战坦克。"
    ]
  },
  "战术建议": {
    "推荐策略": "优先恢复我方单位作战能力，同时采取防御性和欺骗性战术分散敌方注意力，争取时间。",
    "优先目标": "苏-35战斗机（首要威胁），其次是T-90主战坦克。",
    "兵力部署": "集中资源尽快修复歼-20战斗机，同时利用地形设置假目标迷惑敌方，避免直接冲突。",
    "注意事项": [
      "防止敌方空中侦察，保护我方单位免受进一步破坏。",
      "密切监控敌方动向，尤其是苏-35战斗机的活动轨迹。",
      "准备应急撤离计划，避免陷入敌方包围圈。"
    ]
  },
  "作战方案": [
    {
      "方案名称": "拖延战术",
      "执行步骤": [
        "利用地形设置虚假目标，吸引敌方火力，分散其注意力。",
        "通过电子干扰手段掩盖我方真实位置，避免被锁定。",
        "等待我方单位恢复作战能力后，发起反击。",
        "优先摧毁敌方苏-35战斗机，削弱其制空权。"
      ],
      "成功概率": "50%",
      "风险评估": "如果敌方迅速识破虚假目标，可能会导致更大损失；我方单位恢复时间过长将增加风险。"
    },
    {
      "方案名称": "诱敌深入",
      "执行步骤": [
        "布置少量地面部队佯攻，吸引敌方T-90主战坦克进攻。",
        "利用地形设伏，使用反坦克导弹或其他地面武器打击敌方坦克。",
        "同时派遣无人机或电子干扰设备扰乱敌方通信，为地面作战创造机会。"
      ],
      "成功概率": "30%",
      "风险评估": "此方案风险较高，可能导致较大伤亡，且需要我方地面部队具备一定战斗力。"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地形进行快速机动，选择远离敌方主战坦克的方向撤退。",
    "支援需求": "紧急请求空中支援（如盟友战斗机或无人攻击机），以及地面维修团队迅速修复我方单位。",
    "备用方案": "若无法修复歼-20战斗机，考虑采用游击战术，利用地形和电子战手段进行小规模骚扰，拖住敌方。"
  }
}
``` 

### 分析说明：
1. **威胁评估**：
   - 敌方的主要威胁是苏-35战斗机和T-90主战坦克，两者分别代表了空中和地面的核心打击力量。
   - 整体威胁等级为“极高”，因为敌方拥有制空权和强大的地面装甲力量，而我方主力单位目前均不可作战。

2. **力量对比**：
   - 敌方在空中和地面都具备明显优势，尤其是苏-35战斗机的制空能力和T-90主战坦克的火力与防护。
   - 我方的优势在于潜在的歼-20战斗机隐身能力和99A主战坦克的技术参数，但目前这些单位均不可作战，因此难以发挥。
   - 关键弱点包括我方主力单位的不可作战状态和缺乏地面防空能力。

3. **战术建议**：
   - 当前情况下，我方应优先采取拖延和伪装战术，尽量避免直接冲突，争取时间修复我方单位。
   - 利用地形设置虚假目标，分散敌方注意力，同时保护我方核心资产。

4. **作战方案**：
   - “拖延战术”是一种较为稳妥的选择，通过虚假目标和电子干扰分散敌方注意力，为我方争取恢复作战能力的时间。
   - “诱敌深入”是一个高风险方案，适用于我方地面部队有一定战斗能力的情况下，但当前条件下不建议立即实施。

5. **应急预案**：
   - 撤退路线应充分利用平原地形，选择远离敌方主战坦克的方向，减少遭遇战的可能性。
   - 支援需求方面，需要紧急修复我方单位并获得空中支援，尤其是针对敌方苏-35战斗机的威胁。
   - 备用方案则是在无法修复主力单位的情况下，采用游击战术，利用地形和电子战手段进行小规模骚扰。

以上分析综合考虑了敌我双方的力量对比、战场环境及当前态势，旨在为指挥官提供切实可行的战术建议和应急措施。
2025-09-22 11:13:20,196 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:13:20,196 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:13:20,196 - main_fastapi - INFO - 向 19 个客户端广播消息
2025-09-22 11:13:20,200 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758510766791
2025-09-22 11:13:20,200 - main_fastapi - INFO - ================================================================================
2025-09-22 11:13:20,200 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758510766791
2025-09-22 11:13:20,200 - main_fastapi - INFO - 总处理时间: 33404.72ms
2025-09-22 11:13:20,200 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:13:20,200 - main_fastapi - INFO - 消耗tokens: 2919
2025-09-22 11:13:20,200 - main_fastapi - INFO - ================================================================================
2025-09-22 11:13:20,201 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758510766791_20250922_111320.json
2025-09-22 11:13:20,425 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:20,775 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:21,125 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:21,476 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:21,826 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:22,176 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:22,527 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:22,877 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:23,227 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:23,577 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:23,928 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:24,278 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:24,678 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:25,029 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:25,379 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:25,729 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:26,080 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:26,430 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:26,780 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:27,131 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:27,481 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:27,831 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:28,182 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:28,532 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:28,882 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:29,233 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:29,583 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:29,934 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:30,284 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:30,634 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:30,984 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:31,335 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:31,685 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:32,036 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:32,386 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:32,736 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:33,087 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:33,437 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:33,787 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:34,138 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:34,488 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:34,839 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:35,189 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:35,539 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:35,890 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:36,240 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:36,590 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:36,941 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:37,291 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:37,641 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:37,992 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:38,342 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:38,692 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:39,043 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:39,393 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:39,744 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:40,094 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:40,444 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:40,795 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:41,145 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:41,495 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:41,846 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:42,196 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:42,546 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:42,897 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:43,247 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:43,597 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:43,948 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:44,298 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:44,646 - httpx - INFO - HTTP Request: POST http://localhost:8000/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-22 11:13:44,648 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:44,999 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:45,349 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:45,699 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:46,050 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:46,400 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:46,750 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:47,101 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:47,451 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:47,802 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:48,152 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:48,502 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:48,853 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:49,203 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:49,553 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:49,904 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:50,254 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:50,604 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:50,955 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:51,305 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:51,656 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:52,006 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:52,356 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:52,707 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:53,057 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:53,408 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:53,758 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:54,108 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:54,459 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:54,809 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:55,159 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:55,510 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:55,860 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:56,210 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:56,277 - main_fastapi - INFO - ================================================================================
2025-09-22 11:13:56,277 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758510836277
2025-09-22 11:13:56,277 - main_fastapi - INFO - 模拟时间: 1080.0分钟
2025-09-22 11:13:56,277 - main_fastapi - INFO - ================================================================================
2025-09-22 11:13:56,277 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:13:56,277 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:13:56,277 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:13:56,277 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:13:56,277 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:13:56,278 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:13:56,278 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:13:56,278 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:13:56,278 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:13:56,278 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:13:56,326 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:13:56,326 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:13:56,328 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:13:56,328 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 50.56ms
2025-09-22 11:13:56,328 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:13:56,329 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:13:56,376 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:13:56,376 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:13:56,378 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:13:56,378 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 49.53ms
2025-09-22 11:13:56,378 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:13:56,379 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:13:56,427 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:13:56,427 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:13:56,429 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:13:56,429 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 50.33ms
2025-09-22 11:13:56,429 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:13:56,429 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:13:56,476 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:13:56,476 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:13:56,478 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:13:56,478 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 48.26ms
2025-09-22 11:13:56,478 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:13:56,478 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:13:56,527 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:13:56,527 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:13:56,530 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:13:56,530 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.94ms
2025-09-22 11:13:56,530 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:13:56,530 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:13:56,531 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:13:56,531 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:13:56,531 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:13:56,531 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:13:56,531 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.2ms
2025-09-22 11:13:56,531 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:13:56,531 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:13:56,531 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:13:56,531 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:13:56,531 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:13:56,531 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:13:56,531 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:13:56,531 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:13:56,531 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:13:56,531 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:13:56,531 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:13:56,531 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:13:56,531 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:13:56,531 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:13:56,531 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:13:56,531 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:13:56,561 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:56,911 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:57,262 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:57,612 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:57,962 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:58,313 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:58,663 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:59,013 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:13:59,414 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:13:59,764 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:00,115 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:00,465 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:00,815 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:01,166 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:01,516 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:01,866 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:02,217 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:02,567 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:02,917 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:03,268 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:03,618 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:03,969 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:04,319 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:04,669 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:05,020 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:05,370 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:05,720 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:06,071 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:06,421 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:06,771 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:07,122 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:07,472 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:07,822 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:08,173 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:08,523 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:08,873 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:09,224 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:09,574 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:09,924 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:10,275 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:10,625 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:10,975 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:11,326 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:11,676 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:12,027 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:12,377 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:12,727 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:13,078 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:13,428 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:13,778 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:14,129 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:14,479 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:14,830 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:15,180 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:15,530 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:15,881 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:16,231 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:16,581 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:16,932 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:17,282 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:17,633 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:17,983 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:18,333 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:18,684 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:19,034 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:19,384 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:19,735 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:20,085 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:20,435 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:20,786 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:21,136 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:21,486 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:21,837 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:22,187 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:22,538 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:22,888 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:23,238 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:23,589 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:23,939 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:24,289 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:24,640 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:24,990 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:25,341 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:25,691 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:26,041 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:26,392 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:26,742 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:27,092 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:27,443 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:27,793 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:28,143 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:28,494 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:28,844 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:29,194 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:29,545 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:29,587 - src.llm_service - INFO - LLM分析完成 - 耗时: 33056.12ms, 消耗tokens: 2885
2025-09-22 11:14:29,588 - main_fastapi - INFO - 大模型分析成功 - 耗时: 33056.75ms
2025-09-22 11:14:29,588 - main_fastapi - INFO - 消耗tokens: 2885
2025-09-22 11:14:29,588 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:14:29,588 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:14:29,588 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机的空中打击能力以及T-90主战坦克的地面火力与防护能力",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "T-90主战坦克的火力和防护能力强大，适合集群作战",
      "苏-35战斗机具备强大的空中作战能力，威胁范围广",
      "BTR-80装甲运兵车机动性强，能够快速部署步兵"
    ],
    "我方优势": [
      "歼-20战斗机具备隐身能力和超音速巡航性能，若修复后可发挥制空优势",
      "99A主战坦克性能优越，一旦恢复作战状态将具备很强的地面战斗力"
    ],
    "关键弱点": [
      "我方所有单位目前均处于不可作战状态，缺乏即时战斗能力",
      "敌方拥有制空权，对我方地面部队构成重大威胁"
    ]
  },
  "战术建议": {
    "推荐策略": "优先恢复我方单位作战能力，同时利用地形和天气条件进行隐蔽行动，避免直接暴露于敌方火力之下",
    "优先目标": "苏-35战斗机（因为空中威胁最大）",
    "兵力部署": "集中资源尽快修复歼-20战斗机和99A主战坦克，同时利用地形建立临时防御阵地，避免正面冲突",
    "注意事项": [
      "确保修复工作在隐蔽条件下进行，防止敌方侦察或干扰",
      "密切监视敌方动向，尤其是苏-35战斗机的活动轨迹",
      "做好防空准备，防止敌方空中打击"
    ]
  },
  "作战方案": [
    {
      "方案名称": "方案一：优先恢复歼-20战斗机并夺取制空权",
      "执行步骤": [
        "第一步：派遣工程人员迅速修复歼-20战斗机，优先保证其升空能力",
        "第二步：利用夜间或恶劣天气时机，引导歼-20战斗机升空，寻找机会接近敌方苏-35战斗机",
        "第三步：使用歼-20战斗机的隐身和超视距攻击能力，尝试击落苏-35战斗机，夺取局部制空权"
      ],
      "成功概率": "40%",
      "风险评估": "我方歼-20战斗机在修复过程中容易被发现，且敌人可能提前采取措施应对"
    },
    {
      "方案名称": "方案二：利用地形隐蔽，逐步恢复99A主战坦克并实施地面反击",
      "执行步骤": [
        "第一步：利用平原地形中的植被或建筑物作为掩体，将99A主战坦克隐蔽起来，避免敌方侦查",
        "第二步：集中资源修复99A主战坦克，确保其火力和机动性恢复到最佳状态",
        "第三步：待T-90主战坦克进入我方伏击圈时，发动突然袭击，利用地形优势进行近距离交战"
      ],
      "成功概率": "35%",
      "风险评估": "地面交战中，敌方T-90主战坦克的火力和防护优势可能导致我方损失较大"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地形中的道路网络，选择最近的后方基地或补给点作为撤退方向，避开敌方主要火力点",
    "支援需求": "请求空中支援（如无人机侦察或远程打击）、地面增援部队（如步兵和反坦克导弹）以及后勤保障（如弹药和燃料补给）",
    "备用方案": "若无法有效恢复我方单位作战能力，暂时放弃正面接触，改为游击战术，利用地形和天气条件进行骚扰作战"
  }
}
``` 

### 解析说明：
1. **威胁评估**：
   - 敌方的苏-35战斗机和T-90主战坦克是最大的威胁，特别是苏-35战斗机的空中打击能力对整个战场局势影响巨大。
   - 敌方单位分布较分散，但T-90坦克和BTR-80装甲车的位置距离较近，可能协同作战。

2. **力量对比**：
   - 敌方在地面和空中都具备较强的进攻和防御能力，尤其是T-90和苏-35的组合。
   - 我方的优势在于潜在的高科技装备（如歼-20和99A），但目前均无法投入战斗，处于劣势。

3. **战术建议**：
   - 当前情况下，最紧迫的任务是恢复我方单位的作战能力，尤其是歼-20战斗机和99A主战坦克。
   - 优先目标是苏-35战斗机，因为它的存在严重限制了我方地面部队的行动自由。

4. **作战方案**：
   - 方案一侧重于空中作战，通过歼-20战斗机夺取制空权，从而减轻地面压力。
   - 方案二则聚焦于地面作战，利用地形优势逐步恢复99A的战斗力，进行地面反击。

5. **应急预案**：
   - 如果无法有效恢复我方单位，应制定撤退计划，并寻求外部支援，包括空中侦察、地面增援和后勤保障。

通过以上分析，可以为指挥官提供一个清晰的战略框架，帮助他们在当前复杂的战场环境下做出明智决策。
2025-09-22 11:14:29,588 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:14:29,588 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:14:29,588 - main_fastapi - INFO - 向 19 个客户端广播消息
2025-09-22 11:14:29,592 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758510836277
2025-09-22 11:14:29,592 - main_fastapi - INFO - ================================================================================
2025-09-22 11:14:29,592 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758510836277
2025-09-22 11:14:29,592 - main_fastapi - INFO - 总处理时间: 33310.95ms
2025-09-22 11:14:29,592 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:14:29,592 - main_fastapi - INFO - 消耗tokens: 2885
2025-09-22 11:14:29,592 - main_fastapi - INFO - ================================================================================
2025-09-22 11:14:29,593 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758510836277_20250922_111429.json
2025-09-22 11:14:29,895 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:30,245 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:30,596 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:30,946 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:31,296 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:31,647 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:31,997 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:32,347 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:32,698 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:33,048 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:33,398 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:33,749 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:34,099 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:34,449 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:34,800 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:35,150 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:35,500 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:35,851 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:36,201 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:36,552 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:36,902 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:37,252 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:37,603 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:37,953 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:38,303 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:38,654 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:39,004 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:39,354 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:39,705 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:40,055 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:40,405 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:40,756 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:41,106 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:41,456 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:41,807 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:42,157 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:42,507 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:42,858 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:43,208 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:43,558 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:43,909 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:44,259 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:44,610 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:44,960 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:45,310 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:45,661 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:45,958 - httpx - INFO - HTTP Request: POST http://localhost:8000/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-22 11:14:46,011 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:46,361 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:46,712 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:47,062 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:47,412 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:47,763 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:48,163 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:48,514 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:48,864 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:49,214 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:49,565 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:49,915 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:50,265 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:50,616 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:50,966 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:51,317 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:51,667 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:52,017 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:52,368 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:52,718 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:53,068 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:53,419 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:53,769 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:54,120 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:54,470 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:54,820 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:55,171 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:55,521 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:55,871 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:56,222 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:56,572 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:56,922 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:57,273 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:57,623 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:57,973 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:58,324 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:58,674 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:59,025 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:14:59,375 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:14:59,725 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:00,076 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:00,426 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:00,776 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:01,127 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:01,477 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:01,828 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:02,178 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:02,528 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:02,879 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:03,229 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:03,579 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:03,930 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:04,280 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:04,630 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:04,981 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:05,331 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:05,681 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:05,926 - main_fastapi - INFO - ================================================================================
2025-09-22 11:15:05,926 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758510905926
2025-09-22 11:15:05,926 - main_fastapi - INFO - 模拟时间: 1140.0分钟
2025-09-22 11:15:05,926 - main_fastapi - INFO - ================================================================================
2025-09-22 11:15:05,926 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:15:05,926 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:15:05,926 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:15:05,926 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:15:05,926 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:15:05,927 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:15:05,927 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:15:05,927 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:15:05,927 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:15:05,927 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:15:05,986 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:15:05,986 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:15:05,988 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:15:05,988 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 61.75ms
2025-09-22 11:15:05,989 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:15:05,989 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:15:06,032 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:06,040 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:15:06,040 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:15:06,043 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:15:06,043 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 53.74ms
2025-09-22 11:15:06,043 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:15:06,043 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:15:06,098 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:15:06,099 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:15:06,101 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:15:06,101 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 58.28ms
2025-09-22 11:15:06,101 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:15:06,102 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:15:06,150 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:15:06,150 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:15:06,152 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:15:06,152 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 50.59ms
2025-09-22 11:15:06,152 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:15:06,153 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:15:06,203 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:15:06,203 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:15:06,205 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:15:06,206 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 52.63ms
2025-09-22 11:15:06,206 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:15:06,206 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:15:06,206 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:15:06,206 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:15:06,206 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:15:06,206 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:15:06,206 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.23ms
2025-09-22 11:15:06,206 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:15:06,206 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:15:06,206 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:15:06,206 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:15:06,206 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:15:06,207 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:15:06,207 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:15:06,207 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:15:06,207 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:15:06,207 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:15:06,207 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:15:06,207 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:15:06,207 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:15:06,207 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:15:06,207 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:15:06,207 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:15:06,382 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:06,732 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:07,083 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:07,433 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:07,783 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:08,134 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:08,484 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:08,834 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:09,185 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:09,535 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:09,885 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:10,236 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:10,586 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:10,936 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:11,287 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:11,637 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:11,987 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:12,338 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:12,688 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:13,039 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:13,389 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:13,739 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:14,090 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:14,440 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:14,790 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:15,141 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:15,491 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:15,842 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:16,192 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:16,542 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:16,893 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:17,243 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:17,593 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:17,944 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:18,294 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:18,645 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:18,995 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:19,345 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:19,696 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:20,046 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:20,396 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:20,747 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:21,097 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:21,448 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:21,798 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:22,148 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:22,499 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:22,849 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:23,199 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:23,550 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:23,900 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:24,251 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:24,601 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:24,951 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:25,302 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:25,652 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:26,002 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:26,353 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:26,703 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:27,053 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:27,404 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:27,754 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:28,104 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:28,455 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:28,805 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:29,155 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:29,506 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:29,856 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:30,206 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:30,557 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:30,907 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:31,258 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:31,608 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:31,958 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:32,309 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:32,659 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:33,009 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:33,360 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:33,710 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:34,061 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:34,411 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:34,761 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:35,112 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:35,462 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:35,812 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:36,163 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:36,513 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:36,863 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:37,214 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:37,564 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:37,914 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:38,147 - src.llm_service - INFO - LLM分析完成 - 耗时: 31940.11ms, 消耗tokens: 2874
2025-09-22 11:15:38,147 - main_fastapi - INFO - 大模型分析成功 - 耗时: 31940.74ms
2025-09-22 11:15:38,147 - main_fastapi - INFO - 消耗tokens: 2874
2025-09-22 11:15:38,147 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:15:38,147 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:15:38,147 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "苏-35战斗机（空中威胁）和T-90主战坦克（地面威胁），尤其是苏-35的空中打击能力对我方构成严重威胁。",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "拥有制空权（苏-35战斗机）",
      "地面装甲部队（T-90主战坦克）具备强大的火力和防护能力",
      "BTR-80装甲运兵车提供机动性和步兵支援能力"
    ],
    "我方优势": [
      "歼-20战斗机具备隐身能力和超音速巡航能力（如果恢复作战能力）",
      "99A主战坦克在性能上与T-90相当，但目前处于不可作战状态"
    ],
    "关键弱点": [
      "我方所有单位均处于不可作战状态，缺乏有效反击能力",
      "缺乏防空体系，容易被敌方空中力量打击",
      "地形开阔，不利于隐蔽和防守"
    ]
  },
  "战术建议": {
    "推荐策略": "优先恢复我方单位作战能力，同时采取被动防御和诱敌深入战术，利用地形和天气条件尽可能拖延时间，等待增援或修复装备。",
    "优先目标": "敌方苏-35战斗机，因为其空中打击能力对我方构成最大威胁。",
    "兵力部署": "将现有力量分散隐蔽部署，避免暴露于敌方火力范围；优先安排工程和维修人员修复99A主战坦克和歼-20战斗机。",
    "注意事项": [
      "密切监视敌方动向，特别是苏-35战斗机的活动轨迹",
      "利用白天良好能见度进行侦察，但需注意隐蔽，防止被敌方发现",
      "确保通信畅通，以便随时调整战术"
    ]
  },
  "作战方案": [
    {
      "方案名称": "诱敌深入",
      "执行步骤": [
        "利用地形设置陷阱，如反坦克地雷区，吸引敌方T-90和BTR-80进入埋伏圈",
        "调动少量地面部队，制造佯攻，引诱敌方坦克推进",
        "一旦敌方进入预设区域，集中火力打击，优先针对T-90主战坦克"
      ],
      "成功概率": "30%",
      "风险评估": "此方案需要敌方主动进攻，且我方必须在诱敌过程中保持高度隐蔽，否则可能造成更大损失。"
    },
    {
      "方案名称": "空中拦截（假设歼-20恢复作战能力）",
      "执行步骤": [
        "利用歼-20的隐身能力和超视距攻击能力，尝试拦截并摧毁敌方苏-35战斗机",
        "配合地面雷达和预警系统，为歼-20提供目标信息支持",
        "在空中优势建立后，掩护地面部队行动"
      ],
      "成功概率": "40%",
      "风险评估": "歼-20需要在短时间内完成维修并投入战斗，且敌方可能有其他防空手段应对。"
    }
  ],
  "应急预案": {
    "撤退路线": "选择远离敌方主战坦克和装甲车的方向，利用平原地形快速撤离至安全区域。",
    "支援需求": "请求紧急增援，包括地面装甲部队、防空力量和维修保障团队。",
    "备用方案": "若无法恢复作战能力，采用游击战术，利用地形进行小规模伏击，尽量延缓敌方推进速度。"
  }
}
``` 

### 解析说明：
1. **威胁评估**： 
   - 敌方的苏-35战斗机是最大的威胁，因为它可以迅速对我方实施空中打击。
   - T-90主战坦克次之，因其强大的火力和防护能力。
   - BTR-80装甲车虽然威胁较小，但仍需注意其机动性和步兵支援能力。

2. **力量对比**： 
   - 敌方在制空权和地面装甲方面占据明显优势。
   - 我方的优势在于潜在的隐身技术和超音速巡航能力（歼-20），但前提是这些装备能够恢复作战能力。
   - 当前的关键弱点是我方所有单位都无法正常作战，缺乏有效的反击手段。

3. **战术建议**：
   - 由于我方单位不可作战，当前的重点是恢复战斗力并采取被动防御措施。
   - 优先目标是敌方的苏-35战斗机，因为它对整个战场局势影响最大。
   - 兵力应分散部署，避免集中被敌方打击。

4. **作战方案**：
   - **诱敌深入**：通过设置陷阱和佯攻，引诱敌方地面部队进入伏击圈，适合当前我方不可作战的情况。
   - **空中拦截**：假设歼-20能够恢复作战能力，利用其隐身优势打击敌方战机，建立空中优势。

5. **应急预案**：
   - 若无法恢复作战能力，应尽快撤离并寻求增援。
   - 请求地面装甲部队和防空力量支援，以弥补当前的劣势。
   - 如果情况危急，可采用游击战术，拖延敌方推进速度。

该分析综合了当前战场态势、敌我双方优劣势以及环境因素，提供了切实可行的战术建议和应急措施。
2025-09-22 11:15:38,147 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:15:38,147 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:15:38,148 - main_fastapi - INFO - 向 19 个客户端广播消息
2025-09-22 11:15:38,151 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758510905926
2025-09-22 11:15:38,152 - main_fastapi - INFO - ================================================================================
2025-09-22 11:15:38,152 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758510905926
2025-09-22 11:15:38,152 - main_fastapi - INFO - 总处理时间: 32221.48ms
2025-09-22 11:15:38,152 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:15:38,152 - main_fastapi - INFO - 消耗tokens: 2874
2025-09-22 11:15:38,152 - main_fastapi - INFO - ================================================================================
2025-09-22 11:15:38,152 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758510905926_20250922_111538.json
2025-09-22 11:15:38,265 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:38,615 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:38,965 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:39,316 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:39,666 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:40,016 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:40,417 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:40,767 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:41,117 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:41,468 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:41,818 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:42,168 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:42,519 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:42,869 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:43,219 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:43,570 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:43,920 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:44,271 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:44,621 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:44,971 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:45,322 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:45,672 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:46,022 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:46,373 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:46,723 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:47,073 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:47,424 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:47,774 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:48,124 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:48,475 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:48,825 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:49,175 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:49,526 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:49,876 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:50,227 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:50,577 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:50,927 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:51,278 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:51,628 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:51,979 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:52,329 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:52,679 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:53,030 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:53,380 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:53,730 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:54,081 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:54,431 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:54,782 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:55,132 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:55,482 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:55,833 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:56,183 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:56,533 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:56,884 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:57,234 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:57,585 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:57,935 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:58,285 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:15:58,636 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:58,986 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:59,336 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:15:59,687 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:00,037 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:00,387 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:00,738 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:01,088 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:01,438 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:01,789 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:02,139 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:02,490 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:02,840 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:03,190 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:03,541 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:03,891 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:04,241 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:04,592 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:04,942 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:05,292 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:05,643 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:05,993 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:06,343 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:06,694 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:07,044 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:07,394 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:07,744 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:08,095 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:08,445 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:08,795 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:09,146 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:09,496 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:09,846 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:10,197 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:10,547 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:10,898 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:11,248 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:11,598 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:11,949 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:12,299 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:12,649 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:13,000 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:13,350 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:13,700 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:14,051 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:14,401 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:14,751 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:15,102 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:15,452 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:15,635 - main_fastapi - INFO - ================================================================================
2025-09-22 11:16:15,635 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758510975635
2025-09-22 11:16:15,635 - main_fastapi - INFO - 模拟时间: 1200.0分钟
2025-09-22 11:16:15,635 - main_fastapi - INFO - ================================================================================
2025-09-22 11:16:15,635 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:16:15,635 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:16:15,635 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:16:15,635 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:16:15,635 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:16:15,635 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:16:15,636 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:16:15,636 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:16:15,636 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:16:15,636 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:16:15,693 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:16:15,693 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:16:15,695 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:16:15,695 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 59.7ms
2025-09-22 11:16:15,695 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:16:15,696 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:16:15,744 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:16:15,744 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:16:15,746 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:16:15,746 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 50.47ms
2025-09-22 11:16:15,746 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:16:15,747 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:16:15,794 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:16:15,794 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:16:15,796 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:16:15,796 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 49.44ms
2025-09-22 11:16:15,796 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:16:15,797 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:16:15,802 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:15,844 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:16:15,844 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:16:15,846 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:16:15,846 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 49.72ms
2025-09-22 11:16:15,847 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:16:15,847 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:16:15,894 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:16:15,894 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:16:15,897 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:16:15,897 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 49.72ms
2025-09-22 11:16:15,897 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:16:15,897 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:16:15,897 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:16:15,897 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:16:15,897 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:16:15,897 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:16:15,897 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.22ms
2025-09-22 11:16:15,897 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:16:15,897 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:16:15,897 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:16:15,897 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:16:15,898 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:16:15,898 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:16:15,898 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:16:15,898 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:16:15,898 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:16:15,898 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:16:15,898 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:16:15,898 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:16:15,898 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:16:15,898 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:16:15,898 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:16:15,898 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:16:16,153 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:16,503 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:16,853 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:17,204 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:17,554 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:17,904 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:18,255 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:18,605 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:18,955 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:19,306 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:19,656 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:20,006 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:20,357 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:20,707 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:21,057 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:21,408 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:21,758 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:22,109 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:22,459 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:22,809 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:23,160 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:23,510 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:23,860 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:24,211 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:24,561 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:24,911 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:25,262 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:25,612 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:25,962 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:26,313 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:26,663 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:27,013 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:27,364 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:27,714 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:28,064 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:28,415 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:28,765 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:29,115 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:29,466 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:29,816 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:30,167 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:30,517 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:30,867 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:31,218 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:31,568 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:31,918 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:32,269 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:32,619 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:32,969 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:33,320 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:33,670 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:34,021 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:34,371 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:34,721 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:35,072 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:35,422 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:35,772 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:36,123 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:36,473 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:36,823 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:37,174 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:37,524 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:37,874 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:38,225 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:38,575 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:38,925 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:39,276 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:39,626 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:39,976 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:40,327 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:40,677 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:41,027 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:41,377 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:41,728 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:42,078 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:42,428 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:42,779 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:43,129 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:43,479 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:43,830 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:44,180 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:44,531 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:44,881 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:45,231 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:45,582 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:45,932 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:46,283 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:46,633 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:46,983 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:47,334 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:47,684 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:48,034 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:48,385 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:48,735 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:49,085 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:49,436 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:49,547 - src.llm_service - INFO - LLM分析完成 - 耗时: 33649.34ms, 消耗tokens: 2938
2025-09-22 11:16:49,547 - main_fastapi - INFO - 大模型分析成功 - 耗时: 33649.97ms
2025-09-22 11:16:49,547 - main_fastapi - INFO - 消耗tokens: 2938
2025-09-22 11:16:49,548 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:16:49,548 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:16:49,548 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机（空中威胁）和T-90主战坦克（地面威胁）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "拥有强大的空中和地面威胁组合",
      "T-90主战坦克火力和防护性能优异",
      "BTR-80装甲运兵车具备高机动性和两栖能力"
    ],
    "我方优势": [
      "歼-20战斗机具有隐身能力和超音速巡航能力",
      "99A主战坦克在技术性能上与T-90相当，但目前不可作战"
    ],
    "关键弱点": [
      "我方所有单位均处于不可作战状态，无法立即投入战斗",
      "缺乏地面防空和近距离反坦克能力",
      "敌方单位分散，难以集中优势兵力应对"
    ]
  },
  "战术建议": {
    "推荐策略": "优先恢复我方单位的作战能力，同时采取隐蔽机动和电子干扰措施应对敌方空中威胁，再逐步展开地面反击",
    "优先目标": "苏-35战斗机（首要空中威胁）和T-90主战坦克（首要地面威胁）",
    "兵力部署": "将有限资源集中用于恢复我方单位的作战能力，同时利用地形和伪装进行隐蔽，避免直接暴露于敌方火力之下",
    "注意事项": [
      "尽快修复99A主战坦克和歼-20战斗机，使其恢复作战能力",
      "建立临时防空网络，保护地面部队免受空中打击",
      "避免正面硬碰硬，利用地形和战术规避敌方优势"
    ]
  },
  "作战方案": [
    {
      "方案名称": "方案一：空中防御与地面反击",
      "执行步骤": [
        "第一步：通过电子干扰和防空导弹诱骗苏-35战斗机，迫使其降低飞行高度或暴露位置",
        "第二步：利用地面防空系统（如便携式防空导弹）对苏-35战斗机进行精确打击",
        "第三步：恢复99A主战坦克的作战能力后，组织地面反击，重点打击T-90主战坦克"
      ],
      "成功概率": "60%",
      "风险评估": "苏-35战斗机可能利用其高性能发动机和机动性突破干扰，对我方地面部队构成持续威胁；地面反击时T-90的火力和防护可能导致较大损失"
    },
    {
      "方案名称": "方案二：地面防御与空中牵制",
      "执行步骤": [
        "第一步：利用地形和伪装建立地面防御阵地，抵御T-90主战坦克的进攻",
        "第二步：利用远程反坦克导弹和无人机侦察，逐步削弱T-90的战斗力",
        "第三步：等待歼-20战斗机恢复作战能力后，对苏-35战斗机实施超视距打击"
      ],
      "成功概率": "55%",
      "风险评估": "地面防御可能因敌方数量优势而被突破，T-90的集群作战能力较强；歼-20战斗机恢复作战能力前，空中威胁难以有效应对"
    }
  ],
  "应急预案": {
    "撤退路线": "沿平原地区向西北方向撤退，利用地形遮蔽减少暴露，同时寻找有利地形设防拖延敌军追击",
    "支援需求": "紧急请求空中加油支援、地面补给车队以及快速维修团队",
    "备用方案": "如果无法恢复我方单位作战能力，采用游击战术，利用地形和夜战优势骚扰敌方后勤线，争取时间等待增援"
  }
}
``` 

### 解析说明：
1. **威胁评估**：
   - 整体威胁等级为“高”，因为敌方拥有一架苏-35战斗机和一辆T-90主战坦克，分别构成空中和地面的主要威胁。
   - 主要威胁是苏-35战斗机，因其高空高速特性且目前我方无可用空中力量对抗。
   - 威胁排序：苏-35 > T-90 > BTR-80。

2. **力量对比**：
   - 敌方优势在于综合威胁能力强，尤其是苏-35和T-90构成了立体化威胁。
   - 我方优势在于歼-20的隐身能力和99A的技术性能，但目前均不可作战。
   - 关键弱点是我方所有单位处于不可作战状态，缺乏有效的地面防空和反坦克能力。

3. **战术建议**：
   - 优先策略是恢复我方单位作战能力，同时采取电子干扰和防空措施应对敌方空中威胁。
   - 优先打击目标是苏-35和T-90。
   - 部署建议是集中资源恢复我方单位，利用地形和伪装进行隐蔽。

4. **作战方案**：
   - 方案一侧重于空中防御与地面反击，利用电子干扰和防空手段削弱敌方空中威胁，随后地面反击。
   - 方案二侧重于地面防御与空中牵制，利用地形和游击战术拖延敌方，等待歼-20恢复作战能力。

5. **应急预案**：
   - 撤退路线选择西北方向，利用地形遮蔽减少暴露。
   - 支援需求包括空中加油、地面补给和快速维修团队。
   - 备用方案是采用游击战术，骚扰敌方后勤线，争取时间等待增援。

此分析基于当前战场态势，结合地形、天气等因素，提供了客观且实用的战术建议和作战方案。
2025-09-22 11:16:49,548 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:16:49,548 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:16:49,548 - main_fastapi - INFO - 向 19 个客户端广播消息
2025-09-22 11:16:49,552 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758510975635
2025-09-22 11:16:49,552 - main_fastapi - INFO - ================================================================================
2025-09-22 11:16:49,552 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758510975635
2025-09-22 11:16:49,552 - main_fastapi - INFO - 总处理时间: 33912.81ms
2025-09-22 11:16:49,552 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:16:49,552 - main_fastapi - INFO - 消耗tokens: 2938
2025-09-22 11:16:49,552 - main_fastapi - INFO - ================================================================================
2025-09-22 11:16:49,553 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758510975635_20250922_111649.json
2025-09-22 11:16:49,786 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:50,137 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:50,487 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:50,837 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:51,188 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:51,538 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:51,889 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:52,239 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:52,589 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:52,940 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:53,290 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:53,690 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:54,041 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:54,391 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:54,742 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:55,092 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:55,442 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:55,793 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:56,143 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:56,493 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:56,844 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:57,194 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:57,545 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:16:57,895 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:58,245 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:58,596 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:58,946 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:59,296 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:59,647 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:16:59,997 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:00,347 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:00,698 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:01,048 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:01,398 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:01,749 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:02,099 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:02,449 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:02,800 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:03,150 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:03,501 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:03,851 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:04,201 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:04,552 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:04,902 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:05,252 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:05,603 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:05,953 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:06,303 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:06,654 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:07,004 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:07,354 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:07,705 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:08,055 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:08,406 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:08,756 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:09,106 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:09,457 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:09,807 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:10,157 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:10,508 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:10,858 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:11,208 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:11,559 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:11,909 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:12,259 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:12,610 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:12,960 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:13,036 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 20
2025-09-22 11:17:13,247 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 21
2025-09-22 11:17:13,310 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:13,661 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:14,011 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:14,361 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:14,712 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:15,062 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:15,413 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:15,730 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 22
2025-09-22 11:17:15,732 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 23
2025-09-22 11:17:15,763 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:16,113 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:16,464 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:16,814 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:17,164 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:17,515 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:17,865 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:18,215 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:18,566 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:18,916 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:19,266 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:19,617 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:19,967 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:20,318 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:20,668 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:21,018 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:21,369 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:21,650 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 24
2025-09-22 11:17:21,651 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 25
2025-09-22 11:17:21,719 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:22,069 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:22,420 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:22,770 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:23,121 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:23,471 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:23,746 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 26
2025-09-22 11:17:23,747 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 27
2025-09-22 11:17:23,821 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:24,172 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:24,522 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:24,872 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:25,223 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:25,421 - main_fastapi - INFO - ================================================================================
2025-09-22 11:17:25,421 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758511045421
2025-09-22 11:17:25,421 - main_fastapi - INFO - 模拟时间: 1260.0分钟
2025-09-22 11:17:25,421 - main_fastapi - INFO - ================================================================================
2025-09-22 11:17:25,421 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:17:25,421 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:17:25,421 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:17:25,421 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:17:25,421 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:17:25,421 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:17:25,421 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:17:25,422 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:17:25,422 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:17:25,422 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:17:25,475 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:17:25,475 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:17:25,478 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:17:25,478 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 56.01ms
2025-09-22 11:17:25,478 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:17:25,478 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:17:25,527 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:17:25,527 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:17:25,529 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:17:25,529 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.28ms
2025-09-22 11:17:25,529 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:17:25,530 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:17:25,573 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:25,580 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:17:25,580 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:17:25,582 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:17:25,583 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 52.72ms
2025-09-22 11:17:25,583 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:17:25,583 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:17:25,633 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:17:25,633 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:17:25,636 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:17:25,636 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 52.92ms
2025-09-22 11:17:25,636 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:17:25,636 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:17:25,689 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:17:25,689 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:17:25,691 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:17:25,691 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 54.57ms
2025-09-22 11:17:25,691 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:17:25,691 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:17:25,691 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:17:25,691 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:17:25,691 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:17:25,691 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:17:25,691 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.2ms
2025-09-22 11:17:25,691 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:17:25,692 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:17:25,692 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:17:25,692 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:17:25,692 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:17:25,692 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:17:25,692 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:17:25,692 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:17:25,692 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:17:25,692 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:17:25,692 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:17:25,692 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:17:25,692 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:17:25,692 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:17:25,692 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:17:25,692 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:17:25,923 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:26,274 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:26,624 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:26,974 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:27,325 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:27,675 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:28,026 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:28,376 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:28,726 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:29,077 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:29,427 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:29,777 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:30,128 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:30,478 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:30,828 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:31,179 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:31,529 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:31,880 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:32,230 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:32,580 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:32,931 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:33,281 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:33,631 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:33,982 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:34,332 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:34,682 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:35,033 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:35,383 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:35,733 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:36,084 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:36,434 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:36,785 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:37,135 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:37,485 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:37,836 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:38,186 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:38,536 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:38,887 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:39,237 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:39,587 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:39,938 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:40,288 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:40,638 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:40,989 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:41,339 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:41,689 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:42,040 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:42,390 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:42,740 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:43,091 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:43,441 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:43,791 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:44,142 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:44,492 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:44,842 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:45,193 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:45,543 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:45,894 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:46,244 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:46,594 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:46,945 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:47,295 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:47,645 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:47,996 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:48,346 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:48,696 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:49,047 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:49,397 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:49,747 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:50,098 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:50,448 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:50,798 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:51,149 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:51,499 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:51,849 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:52,200 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:52,550 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:52,900 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:53,250 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:53,601 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:53,951 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:54,301 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:54,652 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:55,002 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:55,352 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:55,702 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:56,053 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:56,403 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:56,753 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:57,104 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:57,454 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:57,804 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:58,155 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:58,505 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:58,663 - src.llm_service - INFO - LLM分析完成 - 耗时: 32970.86ms, 消耗tokens: 2913
2025-09-22 11:17:58,663 - main_fastapi - INFO - 大模型分析成功 - 耗时: 32971.47ms
2025-09-22 11:17:58,663 - main_fastapi - INFO - 消耗tokens: 2913
2025-09-22 11:17:58,663 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:17:58,663 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:17:58,663 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机具备极高的空中威胁，T-90主战坦克则在地面形成强大的火力和防护威胁。",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "拥有强大且多样化的作战平台（坦克、装甲车、战斗机）",
      "T-90主战坦克在火力和防护方面表现优异，适合集群作战",
      "苏-35战斗机具备高空高速性能，威胁范围广"
    ],
    "我方优势": [
      "歼-20战斗机具备隐身能力和超音速巡航能力，潜在制空优势",
      "99A主战坦克在性能上与T-90相当，但目前处于不可作战状态"
    ],
    "关键弱点": [
      "我方两个主力单位均处于不可作战状态，缺乏有效反击能力",
      "敌方苏-35战斗机对空中和地面目标均有显著威胁，而我方空中力量无法应对",
      "地形为平原，不利于隐蔽和机动，对我方不利"
    ]
  },
  "战术建议": {
    "推荐策略": "先稳定局势，避免直接冲突，争取时间恢复我方作战能力；同时利用地形和天气条件进行隐蔽部署，减少暴露。",
    "优先目标": "苏-35战斗机，因其具备极高的空中威胁，需优先消除其对地面部队的支援能力。",
    "兵力部署": "在我方单位恢复作战能力前，优先部署防空力量，如便携式防空导弹系统，用于保护地面部队免受空中打击；同时利用现有资源进行伪装和隐蔽。",
    "注意事项": [
      "避免正面与T-90主战坦克交战，因我方目前缺乏有效地面反击力量",
      "警惕敌方BTR-80装甲运兵车的快速机动和步兵输送能力，防止敌方实施机动突袭或渗透",
      "尽量利用地形特征（如道路、植被边缘）进行隐蔽，减少被敌方侦察发现的可能性"
    ]
  },
  "作战方案": [
    {
      "方案名称": "空中防御与地面伪装",
      "执行步骤": [
        "第一步：利用现有防空资源（如便携式防空导弹系统）建立临时防空网，重点针对苏-35战斗机的活动区域。",
        "第二步：组织地面部队进行伪装和隐蔽，利用地形特点减少暴露，避免被敌方侦察发现。",
        "第三步：通过电子干扰设备削弱敌方无人机或侦察机的情报收集能力，为后续行动争取时间。",
        "第四步：一旦我方单位恢复作战能力，立即调整战术，优先打击敌方空中力量，再逐步应对地面威胁。"
      ],
      "成功概率": "60%",
      "风险评估": "如果敌方迅速发现我方防空网布局，可能集中火力突破，导致地面部队暴露于空中打击之下。此外，敌方可能通过地面部队配合空中力量，对我方隐蔽点进行搜索。"
    }
  ],
  "应急预案": {
    "撤退路线": "选择远离敌方主要进攻方向的路线，利用现有地形（如道路网络或河流）作为掩护，避免暴露在开阔地带。",
    "支援需求": "紧急请求空中支援（如其他可用战斗机或无人机），以及地面增援（如恢复我方主战坦克的作战能力）。",
    "备用方案": "若敌方空中力量过于强大，暂时放弃正面冲突，采用游击战术，利用地形进行骚扰和拖延，等待后续支援到位。"
  }
}
``` 

### 解析说明：
1. **威胁评估**：
   - 整体威胁等级为“高”，因为敌方拥有一架高性能战斗机（苏-35）和一辆主战坦克（T-90），且BTR-80也具备一定的威胁。
   - 主要威胁来自苏-35战斗机，因为它可以在空中对地面部队造成巨大威胁。
   - 威胁排序根据威胁的严重性和优先级确定。

2. **力量对比**：
   - 敌方优势在于其多样化的作战平台，特别是T-90和苏-35。
   - 我方优势在于歼-20的潜在制空能力，但由于其不可作战状态，目前无法发挥。
   - 关键弱点是我方两个主力单位均不可作战，且地形不利于隐蔽和机动。

3. **战术建议**：
   - 推荐策略是稳定局势，避免直接冲突，争取时间恢复我方作战能力。
   - 优先目标是苏-35战斗机，因为它是最大的空中威胁。
   - 具体部署建议包括建立临时防空网和进行地面伪装。
   - 注意事项包括避免正面交战、警惕敌方机动能力和利用地形隐蔽。

4. **作战方案**：
   - 提出了一个“空中防御与地面伪装”的方案，分四步实施。
   - 成功概率为60%，但存在被敌方突破防空网的风险。

5. **应急预案**：
   - 撤退路线选择远离敌方主要进攻方向，利用地形掩护。
   - 支援需求包括空中支援和地面增援，尤其是恢复我方主战坦克的作战能力。
   - 备用方案是在敌方空中力量过强时采用游击战术，拖延时间等待支援。

此分析全面考虑了敌我双方的优劣势、关键威胁和机会，并提供了具体的战术建议和应急措施，确保在当前不利情况下尽可能降低风险并争取主动权。
2025-09-22 11:17:58,663 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:17:58,663 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:17:58,664 - main_fastapi - INFO - 向 27 个客户端广播消息
2025-09-22 11:17:58,666 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:17:58,667 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:17:58,667 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:17:58,668 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:17:58,670 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 26
2025-09-22 11:17:58,670 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 25
2025-09-22 11:17:58,670 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 24
2025-09-22 11:17:58,670 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 23
2025-09-22 11:17:58,670 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758511045421
2025-09-22 11:17:58,670 - main_fastapi - INFO - ================================================================================
2025-09-22 11:17:58,670 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758511045421
2025-09-22 11:17:58,670 - main_fastapi - INFO - 总处理时间: 33242.69ms
2025-09-22 11:17:58,670 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:17:58,670 - main_fastapi - INFO - 消耗tokens: 2913
2025-09-22 11:17:58,670 - main_fastapi - INFO - ================================================================================
2025-09-22 11:17:58,671 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758511045421_20250922_111758.json
2025-09-22 11:17:58,673 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 24
2025-09-22 11:17:58,673 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 25
2025-09-22 11:17:58,674 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 26
2025-09-22 11:17:58,674 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 27
2025-09-22 11:17:58,675 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 28
2025-09-22 11:17:58,675 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 29
2025-09-22 11:17:58,707 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 30
2025-09-22 11:17:58,710 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 31
2025-09-22 11:17:58,740 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 32
2025-09-22 11:17:58,740 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 33
2025-09-22 11:17:58,772 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 34
2025-09-22 11:17:58,772 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 35
2025-09-22 11:17:58,855 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:17:59,206 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:59,556 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:59,906 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:17:59,939 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 36
2025-09-22 11:17:59,950 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 37
2025-09-22 11:18:00,256 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:00,607 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:00,738 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 38
2025-09-22 11:18:00,739 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 39
2025-09-22 11:18:00,957 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:01,307 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:01,658 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:02,008 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:02,358 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:02,709 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:02,737 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 40
2025-09-22 11:18:02,737 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 41
2025-09-22 11:18:03,059 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:03,409 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:03,760 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:04,110 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:04,460 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:04,811 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:05,161 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:05,511 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:05,862 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:06,212 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:06,562 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:06,913 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:07,263 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:07,613 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:07,964 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:08,314 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:08,665 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:09,015 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:09,365 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:09,715 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:10,066 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:10,416 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:10,767 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:11,117 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:11,467 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:11,818 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:12,168 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:12,518 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:12,869 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:13,219 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:13,569 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:13,920 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:14,270 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:14,620 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:14,971 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:15,321 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:15,671 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:16,022 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:16,372 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:16,722 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:17,073 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:17,423 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:17,773 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:18,124 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:18,474 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:18,824 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:19,174 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:19,525 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:19,875 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:20,226 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:20,576 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:20,926 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:21,276 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:21,627 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:21,977 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:22,327 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:22,678 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:23,028 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:23,378 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:23,729 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:24,079 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:24,429 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:24,780 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:25,130 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:25,480 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:25,831 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:26,181 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:26,531 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:26,881 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:27,232 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:27,582 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:27,932 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:28,283 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:28,633 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:28,983 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:29,334 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:29,684 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:30,034 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:30,385 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:30,735 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:31,085 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:31,435 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:31,786 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:32,136 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:32,486 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:32,837 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:33,187 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:33,537 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:33,887 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:34,238 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:34,588 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:34,938 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:35,289 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:35,377 - main_fastapi - INFO - ================================================================================
2025-09-22 11:18:35,377 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758511115377
2025-09-22 11:18:35,377 - main_fastapi - INFO - 模拟时间: 1320.0分钟
2025-09-22 11:18:35,377 - main_fastapi - INFO - ================================================================================
2025-09-22 11:18:35,377 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:18:35,377 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:18:35,377 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:18:35,378 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:18:35,378 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:18:35,378 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:18:35,378 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:18:35,378 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:18:35,378 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:18:35,378 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:18:35,426 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:18:35,426 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:18:35,428 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:18:35,428 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 50.35ms
2025-09-22 11:18:35,428 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:18:35,429 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:18:35,475 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:18:35,476 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:18:35,478 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:18:35,478 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 48.81ms
2025-09-22 11:18:35,478 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:18:35,478 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:18:35,524 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:18:35,524 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:18:35,526 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:18:35,526 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 47.83ms
2025-09-22 11:18:35,526 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:18:35,526 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:18:35,572 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:18:35,572 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:18:35,574 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:18:35,574 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 48.03ms
2025-09-22 11:18:35,574 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:18:35,575 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:18:35,620 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:18:35,620 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:18:35,622 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:18:35,622 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 47.76ms
2025-09-22 11:18:35,622 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:18:35,623 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:18:35,623 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:18:35,623 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:18:35,623 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:18:35,623 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:18:35,623 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.26ms
2025-09-22 11:18:35,623 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:18:35,623 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:18:35,623 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:18:35,623 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:18:35,623 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:18:35,623 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:18:35,623 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:18:35,624 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:18:35,624 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:18:35,624 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:18:35,624 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:18:35,624 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:18:35,624 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:18:35,624 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:18:35,624 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:18:35,624 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:18:35,639 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:35,989 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:36,340 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:36,690 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:37,041 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:37,391 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:37,741 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:38,092 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:38,442 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:38,792 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:39,143 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:39,493 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:39,843 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:40,194 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:40,544 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:40,894 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:41,245 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:41,595 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:41,945 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:42,296 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:42,646 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:42,997 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:43,347 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:43,697 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:44,048 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:44,398 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:44,748 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:45,099 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:45,449 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:45,800 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:46,150 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:46,500 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:46,851 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:47,201 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:47,552 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:47,902 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:48,252 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:48,603 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:48,953 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:49,303 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:49,654 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:50,004 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:50,355 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:50,705 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:51,055 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:51,406 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:51,756 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:52,107 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:52,457 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:52,807 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:53,158 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:53,508 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:53,859 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:54,209 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:54,559 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:54,910 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:55,260 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:55,611 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:55,961 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:56,311 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:56,662 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:18:57,012 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:57,362 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:57,713 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:58,063 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:58,414 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:58,764 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:59,114 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:59,465 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:18:59,815 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:00,165 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:00,516 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:00,866 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:01,216 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:01,567 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:01,917 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:02,267 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:02,618 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:02,968 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:03,319 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:03,669 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:04,019 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:04,369 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:04,720 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:05,070 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:05,420 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:05,771 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:06,121 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:06,471 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:06,822 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:07,172 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:07,522 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:07,873 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:08,223 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:08,573 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:08,923 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:09,274 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:09,624 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:09,809 - src.llm_service - INFO - LLM分析完成 - 耗时: 34185.01ms, 消耗tokens: 2958
2025-09-22 11:19:09,809 - main_fastapi - INFO - 大模型分析成功 - 耗时: 34185.64ms
2025-09-22 11:19:09,809 - main_fastapi - INFO - 消耗tokens: 2958
2025-09-22 11:19:09,809 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:19:09,809 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:19:09,809 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机（极高的空中威胁）与T-90主战坦克（强大的地面火力与防护能力）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "T-90主战坦克具备强大的火力与防护能力，适合正面攻坚",
      "苏-35战斗机拥有出色的机动性和超视距打击能力，对我方空中力量构成严重威胁",
      "BTR-80装甲运兵车机动性强，适合快速部署和步兵输送"
    ],
    "我方优势": [
      "歼-20战斗机具备隐身能力和超音速巡航性能，理论上可以实现制空权争夺",
      "99A主战坦克在性能上与T-90相当，但目前处于不可作战状态"
    ],
    "关键弱点": [
      "我方所有单位均处于不可作战状态，缺乏即时战斗能力",
      "缺乏足够的地面防空力量来应对苏-35战斗机的威胁",
      "地形开阔，不利于隐蔽和机动，容易暴露于敌方火力之下"
    ]
  },
  "战术建议": {
    "推荐策略": "尽快恢复我方单位的作战能力，优先争取制空权，同时利用地形进行隐蔽机动，避免正面冲突",
    "优先目标": "苏-35战斗机（因其对整个战场局势影响最大）",
    "兵力部署": "暂时保持分散隐蔽，集中资源修复我方单位，尤其是歼-20战斗机，以便夺取制空权；同时派遣侦察无人机监视敌方动向，为后续行动提供情报支持",
    "注意事项": [
      "避免直接暴露于敌方T-90主战坦克的正面火力下",
      "警惕BTR-80装甲运兵车的快速机动，防止被包围或伏击",
      "注意天气和地形条件，合理利用遮蔽物和地形特征"
    ]
  },
  "作战方案": [
    {
      "方案名称": "方案一：制空权争夺与地面反击",
      "执行步骤": [
        "第一步：立即修复歼-20战斗机并起飞，使用超视距导弹打击苏-35战斗机，争取制空权。",
        "第二步：通过无人机侦察锁定敌方T-90主战坦克的位置，引导歼-20战斗机实施空中打击。",
        "第三步：待99A主战坦克修复后，组织地面反击，配合空中火力对残余敌军进行清理。",
        "第四步：建立地面防空网，防止敌方空中力量再次干预"
      ],
      "成功概率": "60%",
      "风险评估": "我方单位目前无法作战，导致初期可能面临较大压力；苏-35战斗机的威胁可能导致歼-20战斗机损失"
    },
    {
      "方案名称": "方案二：诱敌深入与伏击",
      "执行步骤": [
        "第一步：利用侦察无人机引诱敌方BTR-80装甲运兵车进入预设伏击圈，使用反装甲武器（如反坦克导弹）进行打击。",
        "第二步：利用地形隐蔽，等待敌方T-90主战坦克进攻时，从侧翼发动突袭。",
        "第三步：在敌方空中力量活动较少的时间段，尝试修复99A主战坦克并投入战斗。",
        "第四步：利用地形优势，逐步蚕食敌方力量"
      ],
      "成功概率": "50%",
      "风险评估": "敌方T-90主战坦克的火力和防护能力较强，直接交战风险较高；缺乏制空权可能导致地面部队遭受空中打击"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地区的开阔地形，选择远离敌方主力的方向进行撤退，尽量利用障碍物（如建筑物或植被）作为掩护。",
    "支援需求": "紧急请求空中加油机支援，确保歼-20战斗机的续航能力；派遣维修团队快速修复99A主战坦克。",
    "备用方案": "若无法迅速恢复制空权，考虑利用地形优势进行持久战，逐步消耗敌方资源"
  }
}
``` 

### 分析说明：
1. **威胁评估**：
   - 敌方的主要威胁来自苏-35战斗机和T-90主战坦克，分别在空中和地面形成强大压力。
   - 整体威胁等级为“高”，因为敌方的空中和地面力量都具备较高的战斗力。

2. **力量对比**：
   - 敌方的优势在于其成熟的主战坦克和战斗机组合，能够形成立体化的进攻能力。
   - 我方的优势在于潜在的制空权争夺能力（歼-20战斗机），但由于目前所有单位不可作战，这一优势尚未发挥。

3. **战术建议**：
   - 当前最紧迫的任务是恢复我方单位的作战能力，特别是歼-20战斗机，以期在空中取得优势。
   - 优先打击苏-35战斗机，削弱敌方空中力量，为地面反击创造条件。

4. **作战方案**：
   - 方案一侧重于通过空中力量率先突破敌方防线，争取制空权后再进行地面反击，成功率较高，但初期风险较大。
   - 方案二则通过诱敌深入和伏击的方式逐步削弱敌方力量，但依赖于地形和战术精确执行。

5. **应急预案**：
   - 在不利情况下，需确保有明确的撤退路线，并请求必要的支援，以保障部队安全及后续作战能力的恢复。

此分析综合考虑了战场环境、敌我双方的装备性能和当前状态，提出了切实可行的战术建议和应急措施。
2025-09-22 11:19:09,809 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:19:09,809 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:19:09,809 - main_fastapi - INFO - 向 41 个客户端广播消息
2025-09-22 11:19:09,810 - main_fastapi - ERROR - 广播消息失败: no close frame received or sent
2025-09-22 11:19:09,811 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:19:09,812 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:19:09,812 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:19:09,812 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:19:09,813 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:19:09,813 - main_fastapi - ERROR - 广播消息失败: no close frame received or sent
2025-09-22 11:19:09,814 - main_fastapi - ERROR - 广播消息失败: no close frame received or sent
2025-09-22 11:19:09,815 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:19:09,815 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:19:09,815 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:19:09,815 - main_fastapi - ERROR - 广播消息失败: no close frame received or sent
2025-09-22 11:19:09,817 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 40
2025-09-22 11:19:09,817 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 39
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 38
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 37
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 36
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 35
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 34
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 33
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 32
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 31
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 30
2025-09-22 11:19:09,818 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 29
2025-09-22 11:19:09,818 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758511115377
2025-09-22 11:19:09,818 - main_fastapi - INFO - ================================================================================
2025-09-22 11:19:09,818 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758511115377
2025-09-22 11:19:09,818 - main_fastapi - INFO - 总处理时间: 34432.16ms
2025-09-22 11:19:09,818 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:19:09,818 - main_fastapi - INFO - 消耗tokens: 2958
2025-09-22 11:19:09,818 - main_fastapi - INFO - ================================================================================
2025-09-22 11:19:09,819 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758511115377_20250922_111909.json
2025-09-22 11:19:09,821 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 30
2025-09-22 11:19:09,822 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 31
2025-09-22 11:19:09,865 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 32
2025-09-22 11:19:09,866 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 33
2025-09-22 11:19:09,974 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:10,325 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:10,675 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:11,025 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:11,376 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:11,726 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:12,076 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:12,426 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:12,736 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 34
2025-09-22 11:19:12,736 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 35
2025-09-22 11:19:12,766 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 36
2025-09-22 11:19:12,767 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 37
2025-09-22 11:19:12,777 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:12,799 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 38
2025-09-22 11:19:12,799 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 39
2025-09-22 11:19:13,127 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:13,477 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:13,828 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:14,178 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:14,528 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:14,879 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:15,229 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:15,579 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:15,929 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:16,280 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:16,630 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:16,980 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:17,331 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:17,681 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:18,031 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:18,382 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:18,732 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:19,082 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:19,432 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:19,783 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:20,133 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:20,483 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:20,834 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:21,184 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:21,534 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:21,885 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:22,235 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:22,585 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:22,936 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:23,286 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:23,636 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:23,986 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:24,337 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:24,687 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:25,037 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:25,388 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:25,738 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:26,088 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:26,439 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:26,789 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:27,139 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:27,489 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:27,840 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:28,190 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:28,540 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:28,891 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:29,241 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:29,591 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:29,941 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:30,292 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:30,642 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:30,992 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:31,343 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:31,693 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:32,043 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:32,393 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:32,744 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:33,094 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:33,444 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:33,795 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:34,145 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:34,495 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:34,845 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:35,196 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:35,546 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:35,896 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:36,247 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:36,597 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:36,947 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:37,297 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:37,648 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:37,998 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:38,348 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:38,698 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:39,049 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:39,399 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:39,749 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:40,100 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:40,450 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:40,800 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:41,151 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:41,501 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:41,851 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:42,201 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:42,552 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:42,902 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:43,252 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:43,603 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:43,953 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:44,303 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:44,654 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:45,004 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:45,354 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:45,473 - main_fastapi - INFO - ================================================================================
2025-09-22 11:19:45,473 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758511185473
2025-09-22 11:19:45,474 - main_fastapi - INFO - 模拟时间: 1380.0分钟
2025-09-22 11:19:45,474 - main_fastapi - INFO - ================================================================================
2025-09-22 11:19:45,474 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:19:45,474 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:19:45,474 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:19:45,474 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:19:45,474 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:19:45,474 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:19:45,474 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:19:45,474 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:19:45,474 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:19:45,474 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:19:45,550 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:19:45,550 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:19:45,553 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:19:45,553 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 78.32ms
2025-09-22 11:19:45,553 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:19:45,553 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:19:45,604 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:19:45,604 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:19:45,606 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:19:45,606 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 53.27ms
2025-09-22 11:19:45,606 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:19:45,607 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:19:45,665 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:19:45,665 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:19:45,667 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:19:45,667 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 60.55ms
2025-09-22 11:19:45,667 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:19:45,668 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:19:45,705 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:45,721 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:19:45,721 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:19:45,723 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:19:45,723 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 55.68ms
2025-09-22 11:19:45,724 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:19:45,724 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:19:45,778 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:19:45,778 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:19:45,781 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:19:45,781 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 56.73ms
2025-09-22 11:19:45,781 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:19:45,781 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:19:45,781 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:19:45,781 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:19:45,781 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:19:45,781 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:19:45,781 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.24ms
2025-09-22 11:19:45,781 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:19:45,781 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:19:45,781 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:19:45,781 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:19:45,782 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:19:45,782 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:19:45,782 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:19:45,782 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:19:45,782 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:19:45,782 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:19:45,782 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:19:45,782 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:19:45,782 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:19:45,782 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:19:45,782 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:19:45,782 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:19:46,055 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:46,405 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:46,756 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:47,106 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:47,456 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:47,807 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:48,157 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:48,507 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:48,858 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:49,208 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:49,558 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:49,909 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:50,259 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:50,609 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:50,960 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:51,310 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:51,660 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:52,011 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:52,361 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:52,711 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:53,062 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:53,412 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:53,762 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:54,113 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:54,463 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:54,813 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:55,164 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:55,514 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:55,865 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:56,215 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:56,565 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:56,916 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:57,266 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:57,616 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:57,967 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:58,317 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:58,667 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:59,018 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:19:59,368 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:19:59,719 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:00,069 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:00,419 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:00,770 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:01,120 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:01,470 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:01,821 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:02,171 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:02,521 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:02,872 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:03,222 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:03,573 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:03,923 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:04,273 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:04,624 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:04,974 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:05,324 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:05,675 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:06,025 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:06,375 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:06,726 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:07,076 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:07,426 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:07,777 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:08,127 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:08,477 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:08,828 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:09,178 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:09,528 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:09,879 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:10,229 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:10,579 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:10,930 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:11,280 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:11,630 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:11,981 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:12,331 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:12,681 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:13,032 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:13,382 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:13,732 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:14,083 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:14,433 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:14,783 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:15,134 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:15,484 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:15,744 - src.llm_service - INFO - LLM分析完成 - 耗时: 29961.58ms, 消耗tokens: 2801
2025-09-22 11:20:15,744 - main_fastapi - INFO - 大模型分析成功 - 耗时: 29962.2ms
2025-09-22 11:20:15,744 - main_fastapi - INFO - 消耗tokens: 2801
2025-09-22 11:20:15,744 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:20:15,744 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:20:15,744 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机（空中威胁）和T-90主战坦克（地面威胁）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "拥有强大的空中和地面火力组合（苏-35战斗机与T-90主战坦克）",
      "BTR-80装甲运兵车机动性强，具备两栖能力，可快速部署步兵"
    ],
    "我方优势": [
      "歼-20战斗机具备隐身能力和超音速巡航能力，理论上可在空中占据优势",
      "99A主战坦克性能优越，如果修复后可与T-90抗衡"
    ],
    "关键弱点": [
      "我方所有单位均处于不可作战状态，缺乏战斗力",
      "缺乏有效的防空和地面支援力量"
    ]
  },
  "战术建议": {
    "推荐策略": "优先恢复我方单位战斗状态，同时利用地形和天气进行隐蔽和拖延战术，争取时间建立防御阵地或获取外部支援。",
    "优先目标": "敌方BTR-80装甲运兵车（因其威胁等级较低且易于针对其弱点进行打击）",
    "兵力部署": "在地形掩护下，将我方单位分散部署，避免集中被敌方空中力量打击。优先修复99A主战坦克，为地面作战做准备。",
    "注意事项": [
      "密切关注敌方空中和地面动态，防止敌方发动突然袭击",
      "利用晴朗天气和良好能见度，尝试通过无人机或侦察手段获取更多敌方情报"
    ]
  },
  "作战方案": [
    {
      "方案名称": "隐蔽拖延与修复计划",
      "执行步骤": [
        "利用平原地形中的植被和建筑物进行隐蔽，避免暴露在我方单位不可作战状态下。",
        "派遣小规模侦察队收集敌方详细情报，尤其是敌方主力位置和行动模式。",
        "优先修复99A主战坦克，使其恢复作战能力，同时为歼-20战斗机补充燃料和弹药。",
        "一旦我方单位恢复作战能力，立即组织对我方威胁较小的目标（如BTR-80装甲运兵车）进行打击，逐步削弱敌方力量。",
        "在地面作战的同时，尝试联络友军或请求空中支援，以应对敌方苏-35战斗机的威胁。"
      ],
      "成功概率": "60%",
      "风险评估": "在单位不可作战状态下，我方极易受到敌方突然袭击，特别是来自苏-35战斗机的空中打击。此外，修复过程可能需要较长时间，存在被敌方发现并摧毁的风险。"
    }
  ],
  "应急预案": {
    "撤退路线": "向北撤离至靠近友军或后勤补给线的方向，利用夜间进行撤退，以减少敌方空中侦查的威胁。",
    "支援需求": "紧急请求空中支援（如盟友战斗机或防空导弹部队），以及地面支援（如维修团队和补给车队）。",
    "备用方案": "若无法有效恢复我方单位，考虑使用远程精确打击武器（如无人机或远程火炮）对敌方关键目标进行骚扰，同时寻找机会突围或撤退。"
  }
}
``` 

### 分析说明：
1. **威胁评估**：  
   - 敌方的苏-35战斗机是最大威胁，其高空高速飞行特性使我方难以防御。
   - T-90主战坦克作为地面力量，火力和防护都非常强大，对我方构成重大威胁。
   - BTR-80装甲运兵车虽然威胁较小，但其高机动性和两栖能力仍需重视。

2. **力量对比**：  
   - 敌方在空中和地面都具备较强的综合战力，而我方目前所有单位均不可作战，处于劣势。
   - 我方的优势在于潜在的高科技装备（如歼-20和99A），但前提是必须尽快恢复这些单位的作战能力。

3. **战术建议**：  
   - 当前最紧迫的任务是恢复我方单位的作战能力，同时利用地形和天气条件进行隐蔽，拖延时间以争取主动权。
   - 初期应优先打击威胁较低的BTR-80装甲运兵车，逐步削弱敌方整体实力。

4. **作战方案**：  
   - 推荐的“隐蔽拖延与修复计划”旨在通过隐蔽行动争取时间，同时逐步恢复我方战斗力，逐步削弱敌方。
   - 成功概率为60%，因为该方案依赖于敌方不主动发起大规模进攻，同时我方能够迅速完成修复工作。

5. **应急预案**：  
   - 如果无法有效恢复战斗力，应及时撤退，并寻求外部支援。备用方案包括使用远程打击手段骚扰敌方，为撤退创造有利条件。

此分析综合了敌我双方的实力对比、地形和天气等因素，提供了切实可行的战术建议和应急措施。
2025-09-22 11:20:15,744 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:20:15,744 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:20:15,744 - main_fastapi - INFO - 向 39 个客户端广播消息
2025-09-22 11:20:15,745 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:20:15,746 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:20:15,746 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:20:15,751 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:20:15,752 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:20:15,753 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:20:15,754 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 38
2025-09-22 11:20:15,754 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 37
2025-09-22 11:20:15,754 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 36
2025-09-22 11:20:15,754 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 35
2025-09-22 11:20:15,754 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 34
2025-09-22 11:20:15,754 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 33
2025-09-22 11:20:15,754 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758511185473
2025-09-22 11:20:15,754 - main_fastapi - INFO - ================================================================================
2025-09-22 11:20:15,754 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758511185473
2025-09-22 11:20:15,754 - main_fastapi - INFO - 总处理时间: 30270.64ms
2025-09-22 11:20:15,754 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:20:15,754 - main_fastapi - INFO - 消耗tokens: 2801
2025-09-22 11:20:15,754 - main_fastapi - INFO - ================================================================================
2025-09-22 11:20:15,755 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758511185473_20250922_112015.json
2025-09-22 11:20:15,758 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 34
2025-09-22 11:20:15,834 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:16,185 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:16,535 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:16,885 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:17,236 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:17,586 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:17,936 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:18,287 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:18,637 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:18,744 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 35
2025-09-22 11:20:18,774 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 36
2025-09-22 11:20:18,809 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 37
2025-09-22 11:20:18,842 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 38
2025-09-22 11:20:18,875 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 39
2025-09-22 11:20:18,987 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:19,338 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:19,688 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:20,038 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:20,389 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:20,739 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:21,089 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:21,440 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:21,790 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:22,140 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:22,491 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:22,841 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:23,191 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:23,542 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:23,892 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:24,242 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:24,593 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:24,943 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:25,293 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:25,644 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:25,994 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:26,344 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:26,694 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:27,045 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:27,395 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:27,745 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:28,096 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:28,446 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:28,796 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:29,147 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:29,497 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:29,847 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:30,198 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:30,548 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:30,898 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:31,249 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:31,599 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:31,949 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:32,300 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:32,650 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:33,000 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:33,351 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:33,701 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:34,051 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:34,402 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:34,752 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:35,102 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:35,453 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:35,803 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:36,153 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:36,504 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:36,854 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:37,204 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:37,555 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:37,905 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:38,255 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:38,606 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:38,956 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:39,306 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:39,657 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:40,007 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:40,357 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:40,708 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:41,108 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:41,458 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:41,809 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:42,159 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:42,509 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:42,860 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:43,210 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:43,560 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:43,911 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:44,261 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:44,611 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:44,962 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:45,312 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:45,663 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:46,013 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:46,363 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:46,714 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:47,064 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:47,414 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:47,765 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:48,115 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:48,465 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:48,816 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:49,166 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:49,516 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:49,867 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:50,217 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:50,567 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:50,918 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:51,268 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:51,619 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:51,969 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:52,319 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:52,670 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:52,805 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 40
2025-09-22 11:20:53,020 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:53,370 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:53,721 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:54,071 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:54,421 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:54,772 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:55,122 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:55,463 - main_fastapi - INFO - ================================================================================
2025-09-22 11:20:55,463 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758511255463
2025-09-22 11:20:55,463 - main_fastapi - INFO - 模拟时间: 1440.0分钟
2025-09-22 11:20:55,463 - main_fastapi - INFO - ================================================================================
2025-09-22 11:20:55,463 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:20:55,463 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:20:55,463 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:20:55,463 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:20:55,463 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:20:55,464 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:20:55,464 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:20:55,464 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:20:55,464 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:20:55,464 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:20:55,472 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:55,523 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:20:55,523 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:20:55,525 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:20:55,525 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 61.33ms
2025-09-22 11:20:55,525 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:20:55,526 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:20:55,575 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:20:55,575 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:20:55,577 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:20:55,577 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.24ms
2025-09-22 11:20:55,577 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:20:55,577 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:20:55,626 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:20:55,626 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:20:55,628 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:20:55,629 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.33ms
2025-09-22 11:20:55,629 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:20:55,629 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:20:55,679 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:20:55,679 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:20:55,681 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:20:55,681 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 52.42ms
2025-09-22 11:20:55,681 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:20:55,682 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:20:55,731 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:20:55,731 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:20:55,733 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:20:55,733 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.26ms
2025-09-22 11:20:55,733 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:20:55,733 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:20:55,734 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:20:55,734 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:20:55,734 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:20:55,734 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:20:55,734 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.24ms
2025-09-22 11:20:55,734 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:20:55,734 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:20:55,734 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:20:55,734 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:20:55,734 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:20:55,734 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:20:55,734 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:20:55,734 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:20:55,734 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:20:55,734 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:20:55,734 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:20:55,734 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:20:55,734 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:20:55,734 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:20:55,734 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:20:55,734 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:20:55,823 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:56,173 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:56,524 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:56,874 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:57,224 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:57,575 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:57,925 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:58,275 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:58,626 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:58,976 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:20:59,326 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:20:59,677 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:00,027 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:00,378 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:00,728 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:01,078 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:01,429 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:01,779 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:02,130 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:02,480 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:02,830 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:03,181 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:03,531 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:03,881 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:04,232 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:04,582 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:04,933 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:05,283 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:05,633 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:05,984 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:06,334 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:06,684 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:07,035 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:07,385 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:07,735 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:08,086 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:08,436 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:08,786 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:09,137 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:09,487 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:09,837 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:10,188 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:10,538 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:10,888 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:11,239 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:11,589 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:11,940 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:12,290 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:12,640 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:12,991 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:13,341 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:13,691 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:14,042 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:14,392 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:14,743 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:15,093 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:15,443 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:15,794 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:16,144 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:16,495 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:16,845 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:17,195 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:17,546 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:17,896 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:18,246 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:18,597 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:18,947 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:19,297 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:19,648 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:19,998 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:20,349 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:20,699 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:21,049 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:21,400 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:21,750 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:22,100 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:22,451 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:22,801 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:23,151 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:23,502 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:23,852 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:24,203 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:24,553 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:24,903 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:25,254 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:25,604 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:25,955 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:26,305 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:26,655 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:27,006 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:27,356 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:27,707 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:28,057 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:28,408 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:28,758 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:29,108 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:29,459 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:29,809 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:30,160 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:30,510 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:30,860 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:31,211 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:31,561 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:31,912 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:32,262 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:32,612 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:32,963 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:33,313 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:33,664 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:34,014 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:34,219 - src.llm_service - INFO - LLM分析完成 - 耗时: 38484.55ms, 消耗tokens: 3116
2025-09-22 11:21:34,219 - main_fastapi - INFO - 大模型分析成功 - 耗时: 38485.19ms
2025-09-22 11:21:34,219 - main_fastapi - INFO - 消耗tokens: 3116
2025-09-22 11:21:34,219 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:21:34,219 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:21:34,219 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "苏-35战斗机（空中威胁）与T-90主战坦克（地面威胁），两者分别在空中和地面对我方构成严重威胁。",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "苏-35战斗机具备强大的空中作战能力，其超视距打击能力和先进的航电系统使我方空中单位面临巨大压力。",
      "T-90主战坦克拥有强大的火力和防护能力，且在平原地形下易于发挥其火力压制和装甲突击的优势。",
      "BTR-80装甲运兵车具有高机动性和两栖能力，可以快速部署步兵并提供火力支援，为敌方提供灵活的地面支援。"
    ],
    "我方优势": [
      "歼-20战斗机虽然目前不可作战，但其隐身能力和超音速巡航性能在我方恢复战斗力后将成为重要优势。",
      "99A主战坦克的技术性能与T-90相当，具备与之抗衡的能力，尤其是在平原地形下，其火力和机动性可以有效应对T-90的威胁。",
      "地形平坦开阔，有利于我方进行大规模机动和火力协调。"
    ],
    "关键弱点": [
      "我方两个主力单位均处于不可作战状态，尤其是歼-20战斗机无法即时参与战斗，使空中力量完全失衡。",
      "敌方苏-35战斗机的威胁尚未被有效遏制，可能对我方地面单位造成严重威胁。",
      "地形平坦开阔，虽然利于我方机动，但也使我方单位容易暴露于敌方火力之下。"
    ]
  },
  "战术建议": {
    "推荐策略": "集中资源尽快恢复我方战机和坦克的作战能力，同时利用地形优势进行隐蔽机动，避免直接与敌方坦克和战斗机正面冲突。",
    "优先目标": "苏-35战斗机（优先防空作战，防止其对我方地面部队的空中打击）；其次为T-90主战坦克（一旦我方坦克恢复作战能力，应迅速对其形成压制）。",
    "兵力部署": "将现有地面部队（如步兵单位或轻型装甲车辆）部署至有利地形，利用障碍物进行隐蔽，避免暴露于敌方坦克视线内；同时，立即组织维修团队修复歼-20战斗机和99A主战坦克，优先恢复歼-20的作战能力。",
    "注意事项": [
      "避免与敌方坦克正面交锋，直到我方坦克完全恢复作战能力。",
      "利用地形和障碍物进行隐蔽，减少敌方空中侦查和轰炸的可能性。",
      "优先恢复我方空中力量，确保在苏-35战斗机威胁消除之前争取主动权。",
      "密切监控敌方动向，尤其注意苏-35战斗机的活动轨迹，制定针对性的防空计划。"
    ]
  },
  "作战方案": [
    {
      "方案名称": "方案一：空中优先恢复与防御反击",
      "执行步骤": [
        "第一步：立即派遣维修团队前往歼-20战斗机所在位置，优先修复其发动机和航电系统，使其尽快恢复作战能力。",
        "第二步：利用现有防空设施和地面防空导弹系统，在可能的空中走廊区域设置密集的防空网，全力拦截敌方苏-35战斗机。",
        "第三步：一旦歼-20恢复作战能力，立即出动执行超视距打击任务，优先摧毁敌方苏-35战斗机，解除空中威胁。",
        "第四步：在空中威胁解除后，集中地面部队力量，对T-90主战坦克形成夹击，利用地形优势进行伏击或侧翼包抄。"
      ],
      "成功概率": "60%",
      "风险评估": "如果歼-20未能及时恢复作战能力，我方将面临敌方苏-35战斗机的持续空袭，可能导致地面部队遭受重大损失。"
    },
    {
      "方案名称": "方案二：地面防御与逐步恢复",
      "执行步骤": [
        "第一步：利用现有地面部队（如步兵单位或轻型装甲车）在有利地形建立防御阵地，利用障碍物和伪装技术进行隐蔽，避免直接暴露于敌方坦克火力之下。",
        "第二步：调动工程部队在关键道路和桥梁附近布设地雷和陷阱，减缓敌方BTR-80装甲运兵车的机动速度。",
        "第三步：集中资源优先修复99A主战坦克，使其尽快恢复作战能力，同时继续推进歼-20的维修工作。",
        "第四步：一旦99A主战坦克恢复作战能力，立即对其进行火力支援训练，并准备对T-90主战坦克发起进攻。",
        "第五步：在歼-20恢复作战能力后，配合地面部队进行联合行动，彻底清除敌方威胁。"
      ],
      "成功概率": "55%",
      "风险评估": "在地面部队缺乏空中掩护的情况下，敌方苏-35战斗机可能对防御阵地发动猛烈空袭，导致地面部队承受较大压力。"
    }
  ],
  "应急预案": {
    "撤退路线": "利用现有地形优势，选择远离敌方坦克和战斗机活动区域的疏散路线，优先撤离关键人员和设备。",
    "支援需求": "紧急请求盟友或后方基地提供空中支援（如无人机或远程防空导弹）、维修物资和技术人员，以及地面增援部队。",
    "备用方案": "若短时间内无法恢复任何作战能力，考虑实施诱饵战术，使用假目标吸引敌方注意力，为后续撤退或恢复作战能力争取时间。"
  }
}
``` 

### 分析总结
- **威胁分析**：敌方的主要威胁来自于苏-35战斗机和T-90主战坦克，两者分别在空中和地面对我方形成巨大压力。
- **力量对比**：尽管我方拥有先进的99A主战坦克和歼-20战斗机，但由于当前不可作战的状态，导致整体力量对比处于劣势。
- **战术建议**：优先恢复我方空中力量（歼-20），并结合地面防御措施，逐步化解敌方威胁。
- **作战方案**：提供了两种方案，一种侧重于快速恢复空中力量并进行防御反击，另一种则侧重于地面防御和逐步恢复地面作战能力。
- **应急预案**：在不利情况下，需做好撤退准备，并寻求外部支援以扭转局势。
2025-09-22 11:21:34,219 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:21:34,219 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:21:34,220 - main_fastapi - INFO - 向 40 个客户端广播消息
2025-09-22 11:21:34,221 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 11:21:34,221 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,221 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,221 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,222 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 11:21:34,222 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,223 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,223 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,224 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,224 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,224 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,224 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,226 - main_fastapi - ERROR - 广播消息失败: received 1001 (going away); then sent 1001 (going away)
2025-09-22 11:21:34,227 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 39
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 38
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 37
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 36
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 35
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 34
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 33
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 32
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 31
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 30
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 29
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 28
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 27
2025-09-22 11:21:34,228 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 26
2025-09-22 11:21:34,228 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758511255463
2025-09-22 11:21:34,228 - main_fastapi - INFO - ================================================================================
2025-09-22 11:21:34,228 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758511255463
2025-09-22 11:21:34,228 - main_fastapi - INFO - 总处理时间: 38756.53ms
2025-09-22 11:21:34,228 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:21:34,228 - main_fastapi - INFO - 消耗tokens: 3116
2025-09-22 11:21:34,228 - main_fastapi - INFO - ================================================================================
2025-09-22 11:21:34,229 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758511255463_20250922_112134.json
2025-09-22 11:21:34,364 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:34,715 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:35,065 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:35,416 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:35,766 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:36,117 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:36,244 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 27
2025-09-22 11:21:36,467 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:36,740 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 28
2025-09-22 11:21:36,761 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 29
2025-09-22 11:21:36,793 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 30
2025-09-22 11:21:36,811 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 31
2025-09-22 11:21:36,817 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:36,843 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 32
2025-09-22 11:21:37,167 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:37,518 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:37,868 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:38,218 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:38,569 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:38,919 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:39,269 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:39,620 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:39,970 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:40,321 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:40,671 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:41,021 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:41,372 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:41,722 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:42,072 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:42,423 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:42,773 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:43,123 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:43,474 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:43,824 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:44,225 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:44,575 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:44,925 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:45,276 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:45,626 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:45,976 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:46,327 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:46,677 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:47,027 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:47,378 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:47,728 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:48,078 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:48,429 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:48,779 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:49,129 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:49,479 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:49,830 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:50,180 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:50,530 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:50,881 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:51,231 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:51,581 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:51,932 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:52,282 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:52,632 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:52,983 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:53,333 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:53,683 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:54,033 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:54,384 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:54,734 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:55,084 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:55,435 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:55,785 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:56,135 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:56,486 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:56,836 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:57,186 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:57,537 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:57,887 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:58,237 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:58,588 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:58,938 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:21:59,288 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:59,639 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:21:59,989 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:00,339 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:00,690 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:01,040 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:01,390 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:01,741 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:02,091 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:02,441 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:02,792 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:03,142 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:03,492 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:03,843 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:04,193 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:04,543 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:04,894 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:05,244 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:05,594 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:05,617 - main_fastapi - INFO - ================================================================================
2025-09-22 11:22:05,617 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758511325617
2025-09-22 11:22:05,617 - main_fastapi - INFO - 模拟时间: 1500.0分钟
2025-09-22 11:22:05,617 - main_fastapi - INFO - ================================================================================
2025-09-22 11:22:05,617 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:22:05,617 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:22:05,617 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:22:05,617 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:22:05,617 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:22:05,617 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:22:05,617 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:22:05,618 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:22:05,618 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:22:05,618 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:22:05,669 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:22:05,669 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:22:05,671 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:22:05,671 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 53.7ms
2025-09-22 11:22:05,671 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:22:05,672 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:22:05,721 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:22:05,721 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:22:05,723 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:22:05,723 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.4ms
2025-09-22 11:22:05,723 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:22:05,724 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:22:05,773 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:22:05,773 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:22:05,775 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:22:05,775 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.14ms
2025-09-22 11:22:05,775 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:22:05,775 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:22:05,822 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:22:05,822 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:22:05,824 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:22:05,824 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 48.91ms
2025-09-22 11:22:05,824 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:22:05,825 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:22:05,870 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:22:05,870 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:22:05,873 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:22:05,873 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 48.24ms
2025-09-22 11:22:05,873 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:22:05,873 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:22:05,873 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:22:05,873 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:22:05,873 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:22:05,874 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:22:05,874 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.24ms
2025-09-22 11:22:05,874 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:22:05,874 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:22:05,874 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:22:05,874 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:22:05,874 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:22:05,874 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:22:05,874 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:22:05,874 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:22:05,874 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:22:05,874 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:22:05,874 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:22:05,874 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:22:05,874 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:22:05,874 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:22:05,874 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:22:05,874 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:22:05,945 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:06,295 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:06,645 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:06,996 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:07,346 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:07,696 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:08,047 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:08,397 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:08,797 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:09,148 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:09,498 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:09,848 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:10,198 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:10,549 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:10,899 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:11,250 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:11,600 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:11,950 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:12,301 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:12,651 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:13,001 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:13,352 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:13,702 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:14,053 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:14,403 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:14,753 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:15,104 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:15,454 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:15,805 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:16,155 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:16,505 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:16,856 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:17,206 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:17,556 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:17,907 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:18,257 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:18,607 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:18,958 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:19,308 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:19,659 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:20,009 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:20,359 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:20,710 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:21,060 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:21,410 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:21,761 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:22,111 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:22,461 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:22,812 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:23,162 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:23,513 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:23,863 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:24,213 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:24,564 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:24,914 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:25,264 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:25,615 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:25,965 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:26,365 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:26,716 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:27,066 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:27,417 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:27,767 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:28,117 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:28,468 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:28,818 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:29,168 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:29,519 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:29,869 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:30,219 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:30,570 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:30,920 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:31,271 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:31,621 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:31,971 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:32,322 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:32,672 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:33,023 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:33,373 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:33,723 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:34,074 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:34,424 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:34,774 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:35,125 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:35,475 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:35,825 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:36,176 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:36,526 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:36,877 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:37,227 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:37,577 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:37,928 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:38,278 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:38,629 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:38,979 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:39,329 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:39,680 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:40,030 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:40,430 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:40,712 - src.llm_service - INFO - LLM分析完成 - 耗时: 34837.77ms, 消耗tokens: 2980
2025-09-22 11:22:40,712 - main_fastapi - INFO - 大模型分析成功 - 耗时: 34838.43ms
2025-09-22 11:22:40,712 - main_fastapi - INFO - 消耗tokens: 2980
2025-09-22 11:22:40,712 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:22:40,712 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:22:40,712 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机（空中威胁）与T-90主战坦克（地面威胁）的协同攻击对我方构成最大威胁。",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": ["强大的空中力量（苏-35战斗机）", "坚固的地面装甲（T-90主战坦克）", "高度机动性（BTR-80装甲运兵车）"],
    "我方优势": ["歼-20战斗机的隐身能力和超音速性能（潜在空中优势）", "99A主战坦克的强大火力（待修复后可发挥）"],
    "关键弱点": ["我方所有单位目前均处于不可作战状态", "缺乏地面部队的直接支援", "空地协同能力不足"]
  },
  "战术建议": {
    "推荐策略": "先恢复我方单位作战能力，同时利用地形和天气条件进行隐蔽机动，避免直接冲突；优先争取空中优势。",
    "优先目标": "苏-35战斗机（空中威胁优先）",
    "兵力部署": "集中资源优先修复歼-20战斗机，使其具备作战能力；同时为99A主战坦克补充弹药和燃料，尽快使其恢复战斗力。",
    "注意事项": ["避免暴露，防止敌方精确打击", "保持通信畅通，确保指挥调度高效", "密切监视敌方动向，及时调整战术"]
  },
  "作战方案": [
    {
      "方案名称": "空中制权+地面反击",
      "执行步骤": [
        "第一步：优先恢复歼-20战斗机的作战能力，利用其隐身优势，尝试在夜间或复杂气象条件下接近敌方，破坏其防空网络。",
        "第二步：一旦歼-20取得空中优势，立即派遣无人机或侦察机确认T-90主战坦克的位置，为后续地面反击做准备。",
        "第三步：待99A主战坦克修复完毕，配合空中掩护，采取侧翼包抄战术，利用地形隐蔽接近敌方T-90主战坦克，实施近距离攻击。",
        "第四步：若BTR-80装甲运兵车出现大规模行动，使用反装甲武器布设伏击点，或通过空中火力对其实施打击。"
      ],
      "成功概率": "60%",
      "风险评估": "敌方可能提前察觉我方意图，对我方修复单位发动突然袭击；苏-35战斗机的空中优势可能导致我方空中行动失败。"
    },
    {
      "方案名称": "诱敌深入+伏击",
      "执行步骤": [
        "第一步：利用虚假信号或佯动吸引敌方T-90主战坦克和BTR-80装甲运兵车进入预设伏击区域。",
        "第二步：待敌方进入伏击圈后，使用反坦克导弹、反装甲地雷或其他隐蔽手段对其实施致命打击。",
        "第三步：同时派遣歼-20战斗机对苏-35战斗机进行精确打击，争取空中优势。",
        "第四步：在地面战斗中，利用地形优势，组织局部反击，逐步瓦解敌方力量。"
      ],
      "成功概率": "50%",
      "风险评估": "敌方可能识破诱饵，导致伏击失败；地面伏击点可能被敌方侦查发现，反而成为我方薄弱环节。"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地形的开阔性，选择远离敌方主战装备的方向作为撤退路线，尽量远离敌方可能设置的地雷或伏击点。",
    "支援需求": "请求空中加油机支援，确保歼-20战斗机续航能力；请求地面装甲部队增援，协助99A主战坦克修复并投入战斗。",
    "备用方案": "若无法恢复歼-20战斗机作战能力，立即转向地面防御战术，利用地形建立临时防线，等待增援。"
  }
}
``` 

### 解析说明：
1. **威胁评估**：
   - 敌方的主要威胁是苏-35战斗机，其高空高速性能和先进的航电系统构成了极大的空中威胁。
   - T-90主战坦克则在地面形成强大的火力和防护威胁，是我方必须优先应对的目标。
   - BTR-80装甲运兵车虽然威胁等级较低，但其高机动性和两栖能力仍需引起注意。

2. **力量对比**：
   - 敌方在空中和地面都拥有较强的实力，尤其是苏-35战斗机和T-90主战坦克的组合极具威慑力。
   - 我方的优势在于歼-20战斗机的隐身和超音速性能，以及99A主战坦克的潜在火力，但目前两者均不可作战，是关键弱点。

3. **战术建议**：
   - 由于我方单位目前无法作战，优先目标是恢复歼-20战斗机的作战能力，争取空中优势。
   - 利用地形和天气条件，避免直接冲突，寻找合适的时机和方式恢复我方战斗力。

4. **作战方案**：
   - **方案一**：通过恢复歼-20战斗机的作战能力，争取空中优势，随后配合地面反击。
   - **方案二**：采用诱敌深入战术，利用伏击消灭敌方地面力量，同时争取空中优势。
   - 两种方案均面临较大风险，尤其是敌方可能提前察觉我方意图。

5. **应急预案**：
   - 若无法恢复歼-20战斗机作战能力，应迅速转向地面防御战术，建立防线等待增援。
   - 请求空中加油机和地面装甲部队增援，提升我方持续作战能力。

此分析基于当前战场态势和单位状态，旨在提供一个全面且务实的战术建议框架。
2025-09-22 11:22:40,712 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:22:40,712 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:22:40,713 - main_fastapi - INFO - 向 32 个客户端广播消息
2025-09-22 11:22:40,718 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:22:40,718 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:22:40,719 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:22:40,719 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:22:40,719 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:22:40,719 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:22:40,720 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 31
2025-09-22 11:22:40,720 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 30
2025-09-22 11:22:40,720 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 29
2025-09-22 11:22:40,720 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 28
2025-09-22 11:22:40,720 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 27
2025-09-22 11:22:40,720 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 26
2025-09-22 11:22:40,720 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758511325617
2025-09-22 11:22:40,720 - main_fastapi - INFO - ================================================================================
2025-09-22 11:22:40,720 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758511325617
2025-09-22 11:22:40,720 - main_fastapi - INFO - 总处理时间: 35095.59ms
2025-09-22 11:22:40,720 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:22:40,720 - main_fastapi - INFO - 消耗tokens: 2980
2025-09-22 11:22:40,720 - main_fastapi - INFO - ================================================================================
2025-09-22 11:22:40,721 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758511325617_20250922_112240.json
2025-09-22 11:22:40,781 - watchfiles.main - INFO - 3 changes detected
2025-09-22 11:22:41,131 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:41,482 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:41,832 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:42,183 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:42,533 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:42,751 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 27
2025-09-22 11:22:42,883 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:43,234 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:43,584 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:43,732 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 28
2025-09-22 11:22:43,764 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 29
2025-09-22 11:22:43,797 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 30
2025-09-22 11:22:43,815 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 31
2025-09-22 11:22:43,847 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 32
2025-09-22 11:22:43,935 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:44,285 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:44,635 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:44,986 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:45,336 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:45,686 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:46,037 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:46,387 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:46,738 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:47,088 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:47,438 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:47,789 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:48,139 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:48,489 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:48,840 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:49,190 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:49,541 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:49,891 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:50,241 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:50,592 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:50,942 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:51,293 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:51,643 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:51,993 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:52,344 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:52,694 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:53,045 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:53,395 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:53,745 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:54,096 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:54,496 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:54,846 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:55,197 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:55,547 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:55,898 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:56,248 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:56,598 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:56,949 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:57,299 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:57,649 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:58,000 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:58,350 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:58,700 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:22:59,051 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:59,401 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:22:59,752 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:00,102 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:00,452 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:00,803 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:01,153 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:01,554 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:01,904 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:02,254 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:02,605 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:02,955 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:03,306 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:03,656 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:04,006 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:04,357 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:04,707 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:05,057 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:05,408 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:05,758 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:06,109 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:06,459 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:06,809 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:07,160 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:07,510 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:07,860 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:08,211 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:08,561 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:08,912 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:09,262 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:09,612 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:09,963 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:10,313 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:10,663 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:11,014 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:11,364 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:11,714 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:12,115 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:12,465 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:12,816 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:13,166 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:13,516 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:13,867 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:14,217 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:14,568 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:14,918 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:15,268 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:15,619 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:15,969 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:15,978 - main_fastapi - INFO - ================================================================================
2025-09-22 11:23:15,978 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758511395978
2025-09-22 11:23:15,978 - main_fastapi - INFO - 模拟时间: 1560.0分钟
2025-09-22 11:23:15,978 - main_fastapi - INFO - ================================================================================
2025-09-22 11:23:15,978 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 11:23:15,978 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 11:23:15,978 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 117.09887996680189,
    "latitude": 40.45334744918589,
    "altitude": 45.2
  },
  "status": {
    "health": 71.56143756135617,
    "ammo": 57.52890814481807,
    "fuel": 4.5424483113142715,
    "operational": false
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T10:57:24.184849",
  "speed": 25.0,
  "heading": 17.333012982752052
}
2025-09-22 11:23:15,978 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.80293299447314,
    "latitude": 39.084035679212406,
    "altitude": 52.1
  },
  "status": {
    "health": 85.31238774998197,
    "ammo": 43.31729890998876,
    "fuel": 4.407745155003896,
    "operational": false
  },
  "threat_level": "中",
  "confidence": 0.5360329021310098,
  "last_seen": "2025-09-22T10:56:50.115830",
  "speed": 35.0,
  "heading": 182.83853828463222
}
2025-09-22 11:23:15,978 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 100.22997208458102,
    "latitude": 26.4025804352423,
    "altitude": 8964.**********
  },
  "status": {
    "health": 87.38596457380363,
    "ammo": 59.84242817276526,
    "fuel": 3.8151134383380687,
    "operational": false
  },
  "threat_level": "极高",
  "confidence": 0.5116589398178008,
  "last_seen": "2025-09-22T10:56:36.485401",
  "speed": 800.0,
  "heading": 193.13492250852704
}
2025-09-22 11:23:15,978 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 11:23:15,978 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 115.41097815743173,
    "latitude": 39.81814043058103,
    "altitude": 48.5
  },
  "status": {
    "health": 91.19941064655549,
    "ammo": 70.86842177244681,
    "fuel": 4.696737556100531,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:07.146854",
  "speed": 30.0,
  "heading": 198.01992457306352
}
2025-09-22 11:23:15,979 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 136.22298793568336,
    "latitude": 21.7197458939119,
    "altitude": 8786.385622715296
  },
  "status": {
    "health": 80.85751153756257,
    "ammo": 88.7541073959243,
    "fuel": 4.101315997422302,
    "operational": false
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T10:57:17.373761",
  "speed": 900.0,
  "heading": 38.168675281910915
}
2025-09-22 11:23:15,979 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 11:23:15,979 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 11:23:16,027 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:23:16,027 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:23:16,029 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 11:23:16,029 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 50.5ms
2025-09-22 11:23:16,029 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:23:16,030 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 11:23:16,078 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:23:16,078 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:23:16,080 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 11:23:16,081 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.0ms
2025-09-22 11:23:16,081 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:23:16,081 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 11:23:16,128 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:23:16,128 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:23:16,130 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 11:23:16,130 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 49.17ms
2025-09-22 11:23:16,130 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:23:16,131 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 11:23:16,181 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:23:16,181 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:23:16,183 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 11:23:16,183 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 52.48ms
2025-09-22 11:23:16,183 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 11:23:16,183 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 11:23:16,232 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 11:23:16,232 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 11:23:16,234 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 11:23:16,234 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 50.77ms
2025-09-22 11:23:16,234 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 11:23:16,235 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 11:23:16,235 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 11:23:16,235 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 11:23:16,235 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 11:23:16,235 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 11:23:16,235 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.2ms
2025-09-22 11:23:16,235 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 11:23:16,235 - main_fastapi - INFO - 用户提示词长度: 2066 字符
2025-09-22 11:23:16,235 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 11:23:16,235 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 11:23:16,235 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 11:23:16,235 - main_fastapi - INFO - LLM服务配置:
2025-09-22 11:23:16,235 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 11:23:16,235 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:23:16,235 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 11:23:16,235 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 11:23:16,235 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 11:23:16,235 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 11:23:16,235 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度117.09887996680189, 纬度40.45334744918589, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.80293299447314, 纬度39.084035679212406, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5360329021310098

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度100.22997208458102, 纬度26.4025804352423, 高度8964.**********m
- 威胁等级: 极高
- 置信度: 0.5116589398178008


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度115.41097815743173, 纬度39.81814043058103, 高度48.5m
- 状态: 不可作战
- 健康度: 91.19941064655549%
- 弹药状态: 70.86842177244681%
- 燃料状态: 4.696737556100531%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度136.22298793568336, 纬度21.7197458939119, 高度8786.385622715296m
- 状态: 不可作战
- 健康度: 80.85751153756257%
- 弹药状态: 88.7541073959243%
- 燃料状态: 4.101315997422302%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 11:23:16,235 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 11:23:16,235 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 11:23:16,236 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 11:23:16,320 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:16,670 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:17,020 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:17,371 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:17,721 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:18,071 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:18,422 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:18,772 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:19,122 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:19,473 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:19,823 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:20,174 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:20,524 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:20,874 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:21,225 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:21,575 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:21,925 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:22,276 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:22,676 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:23,027 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:23,377 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:23,727 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:24,078 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:24,428 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:24,779 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:25,129 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:25,479 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:25,830 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:26,180 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:26,530 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:26,881 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:27,231 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:27,581 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:27,931 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:28,282 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:28,632 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:28,982 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:29,333 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:29,683 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:30,033 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:30,384 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:30,734 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:31,084 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:31,435 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:31,785 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:32,135 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:32,486 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:32,836 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:33,186 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:33,537 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:33,887 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:34,237 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:34,588 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:34,938 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:35,288 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:35,639 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:35,989 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:36,339 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:36,690 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:37,040 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:37,391 - watchfiles.main - INFO - 2 changes detected
2025-09-22 11:23:37,741 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:38,091 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:38,442 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:38,792 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:39,142 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:39,493 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:39,843 - watchfiles.main - INFO - 1 change detected
2025-09-22 11:23:40,243 - watchfiles.main - INFO - 3 changes detected
2025-09-22 11:23:58,308 - src.llm_service - INFO - LLM分析完成 - 耗时: 42072.99ms, 消耗tokens: 3251
2025-09-22 11:23:58,309 - main_fastapi - INFO - 大模型分析成功 - 耗时: 42073.63ms
2025-09-22 11:23:58,309 - main_fastapi - INFO - 消耗tokens: 3251
2025-09-22 11:23:58,309 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 11:23:58,309 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 11:23:58,309 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机（极高的空中威胁）和T-90主战坦克（强大的地面火力和防护）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "苏-35战斗机具备强大的空中作战能力，威胁极大；T-90主战坦克拥有强大火力和防护，适合集群作战；BTR-80装甲运兵车具有高机动性和两栖能力。",
      "敌方单位分布广泛，可能形成多方向威胁，且苏-35战斗机的存在使我方面临空中与地面双重压力"
    ],
    "我方优势": [
      "歼-20战斗机在隐身能力和超音速巡航方面具有显著优势，能够进行超视距攻击和隐身突防。",
      "99A主战坦克在性能上与T-90旗鼓相当，但目前处于不可作战状态，无法立即投入战斗"
    ],
    "关键弱点": [
      "我方所有单位均处于不可作战状态，缺乏即时作战能力。",
      "苏-35战斗机的高空巡逻和快速反应能力对我方构成重大威胁，尤其是在缺乏制空权的情况下。",
      "地面部队缺乏有效的防空手段，容易受到敌方空中打击"
    ]
  },
  "战术建议": {
    "推荐策略": "首先恢复我方单位的作战能力，尤其是歼-20战斗机和99A主战坦克的状态，其次集中力量应对苏-35战斗机的空中威胁，再逐步削弱地面敌军。",
    "优先目标": "苏-35战斗机（因其极高的威胁等级），其次是T-90主战坦克（因其强大的火力和防护）。",
    "兵力部署": "在我方单位恢复作战能力后，优先部署歼-20战斗机夺取制空权，随后使用99A主战坦克配合地面部队对T-90主战坦克和BTR-80装甲运兵车实施打击。",
    "注意事项": [
      "尽快修复我方单位的作战能力，确保其能够投入战斗。",
      "重点防范敌方苏-35战斗机的空中打击，必要时寻求空中掩护或地面防空支持。",
      "利用地形和天气条件减少我方暴露，避免正面冲突，优先采用隐蔽和迂回战术。",
      "密切监控敌方单位动向，特别是苏-35战斗机的活动区域，及时调整作战计划"
    ]
  },
  "作战方案": [
    {
      "方案名称": "方案1：先期空中对抗",
      "执行步骤": [
        "迅速修复歼-20战斗机，使其具备作战能力。",
        "利用歼-20的隐身和超音速巡航能力，对苏-35战斗机进行超视距攻击，争取在短时间内削弱敌方空中力量。",
        "同时，派遣无人机或其他侦察手段监视T-90主战坦克和BTR-80装甲运兵车的位置，为后续地面作战提供情报支持。",
        "一旦歼-20取得制空权，立即部署99A主战坦克和其他地面部队，对T-90主战坦克和BTR-80装甲运兵车发起进攻。"
      ],
      "成功概率": "60%",
      "风险评估": "如果歼-20未能及时修复或在空中对抗中失利，可能导致敌方空中力量对我方地面部队造成严重威胁。此外，苏-35战斗机的先进雷达和导弹系统可能对我方隐身战机构成挑战。"
    },
    {
      "方案名称": "方案2：地面渗透与伏击",
      "执行步骤": [
        "暂时搁置空中对抗，集中资源修复99A主战坦克。",
        "利用地形隐蔽，派遣特种部队或无人侦察机渗透至敌方T-90主战坦克后方，寻找机会对其实施伏击。",
        "同时，利用无人机持续监控敌方BTR-80装甲运兵车的动向，避免其对我方渗透部队造成威胁。",
        "一旦99A主战坦克修复完毕，立即协同地面部队对敌方地面单位展开攻势，同时请求空中支援（如可用）以应对苏-35战斗机的威胁。"
      ],
      "成功概率": "50%",
      "风险评估": "此方案风险较高，因为我方地面部队在缺乏空中掩护的情况下，容易受到苏-35战斗机的空中打击。此外，渗透行动可能被敌方发现，导致我方损失。"
    }
  ],
  "应急预案": {
    "撤退路线": "选择远离敌方单位的方向撤退，优先利用山区或森林地带作为掩护，避免直接暴露于敌方火力范围内。",
    "支援需求": "紧急请求后勤支援，加快我方单位的修复进度；同时请求增派防空力量，协助抵御苏-35战斗机的空中威胁。",
    "备用方案": "若我方单位短期内无法恢复作战能力，考虑采取防御姿态，利用地形优势建立临时防线，等待后续支援力量到位后再行反击。"
  }
}
``` 

### 分析说明：
1. **威胁评估**：
   - 敌方的主要威胁是苏-35战斗机，因其具备极高的空中作战能力，对我方构成最大威胁。
   - T-90主战坦克由于其强大的火力和防护，也是地面作战中的重要威胁。
   - BTR-80装甲运兵车虽然威胁等级较低，但其高机动性和两栖能力仍需重视。

2. **力量对比**：
   - 敌方优势在于苏-35战斗机的强大空中威胁以及T-90主战坦克的地面火力和防护。
   - 我方的优势在于歼-20战斗机的隐身能力和超音速巡航，但在当前状态下，所有单位均不可作战，这成为关键弱点。

3. **战术建议**：
   - 优先目标是苏-35战斗机，因为其空中威胁最为紧迫。
   - 在我方单位修复前，应尽量避免与敌方正面对抗，利用地形和天气条件减少暴露。
   - 一旦我方单位恢复作战能力，应迅速夺取制空权并协同地面部队对敌方地面单位展开攻击。

4. **作战方案**：
   - 方案1侧重于通过歼-20战斗机夺取制空权，随后配合地面部队对敌方地面单位展开攻击。这是较为理想的方案，但成功与否取决于歼-20的修复速度和空中对抗的结果。
   - 方案2则更注重地面渗透和伏击，适用于我方空中力量暂时无法投入的情况，但风险较高，因为空中威胁未解决。

5. **应急预案**：
   - 若我方单位短期内无法恢复作战能力，应采取防御姿态，利用地形优势建立临时防线，同时请求支援以应对敌方威胁。

### 总结：
当前局势对我方不利，但我方可通过尽快修复单位并合理调配资源来扭转局面。务必优先应对苏-35战斗机的空中威胁，同时利用地形和天气条件减少暴露，确保我方单位的安全和作战效率。
2025-09-22 11:23:58,309 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 11:23:58,309 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 11:23:58,309 - main_fastapi - INFO - 向 32 个客户端广播消息
2025-09-22 11:23:58,310 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:23:58,310 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:23:58,311 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:23:58,314 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:23:58,314 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:23:58,315 - main_fastapi - ERROR - 广播消息失败: received 1005 (no status received [internal]); then sent 1005 (no status received [internal])
2025-09-22 11:23:58,317 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 31
2025-09-22 11:23:58,317 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 30
2025-09-22 11:23:58,317 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 29
2025-09-22 11:23:58,317 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 28
2025-09-22 11:23:58,317 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 27
2025-09-22 11:23:58,317 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 26
2025-09-22 11:23:58,317 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758511395978
2025-09-22 11:23:58,317 - main_fastapi - INFO - ================================================================================
2025-09-22 11:23:58,317 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758511395978
2025-09-22 11:23:58,317 - main_fastapi - INFO - 总处理时间: 42331.11ms
2025-09-22 11:23:58,317 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 11:23:58,317 - main_fastapi - INFO - 消耗tokens: 3251
2025-09-22 11:23:58,317 - main_fastapi - INFO - ================================================================================
2025-09-22 11:23:58,318 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758511395978_20250922_112358.json
