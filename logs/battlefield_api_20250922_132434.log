2025-09-22 13:24:34,230 - __main__ - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250922_132434.log
2025-09-22 13:24:34,481 - main_fastapi - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250922_132434.log
2025-09-22 13:24:34,481 - main_fastapi - INFO - 战场态势分析API服务启动
2025-09-22 13:24:36,858 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 1
2025-09-22 13:24:37,745 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 2
2025-09-22 13:24:46,517 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 3
2025-09-22 13:25:07,118 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 4
2025-09-22 13:25:09,749 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 5
2025-09-22 13:25:44,157 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 6
2025-09-22 13:25:49,746 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 7
2025-09-22 13:26:21,188 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 8
2025-09-22 13:26:31,601 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 9
2025-09-22 13:26:31,613 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 10
2025-09-22 13:26:32,906 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 11
2025-09-22 13:27:10,747 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 12
2025-09-22 13:27:11,750 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 13
2025-09-22 13:27:12,012 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 14
2025-09-22 13:27:12,012 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 15
2025-09-22 13:27:52,089 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 16
2025-09-22 13:27:52,118 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 17
2025-09-22 13:27:52,307 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 18
2025-09-22 13:27:52,325 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 19
2025-09-22 13:28:00,122 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 20
2025-09-22 13:28:00,122 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 21
2025-09-22 13:28:13,275 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 22
2025-09-22 13:28:16,742 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 23
2025-09-22 13:28:30,860 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 24
2025-09-22 13:28:33,753 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 25
2025-09-22 13:28:38,748 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 26
2025-09-22 13:28:38,774 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 27
2025-09-22 13:29:10,486 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 28
2025-09-22 13:29:12,753 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 29
2025-09-22 13:29:12,778 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 30
2025-09-22 13:29:12,811 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 31
2025-09-22 13:29:23,357 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 32
2025-09-22 13:29:27,533 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 33
2025-09-22 13:29:29,745 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 34
2025-09-22 13:29:29,770 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 35
2025-09-22 13:29:29,799 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 36
2025-09-22 13:29:38,221 - main_fastapi - INFO - ================================================================================
2025-09-22 13:29:38,221 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758518978221
2025-09-22 13:29:38,221 - main_fastapi - INFO - 模拟时间: 60.0分钟
2025-09-22 13:29:38,221 - main_fastapi - INFO - ================================================================================
2025-09-22 13:29:38,221 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 13:29:38,221 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 13:29:38,221 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 116.52398971859019,
    "latitude": 40.08739464824365,
    "altitude": 45.2
  },
  "status": {
    "health": 82.24366534171065,
    "ammo": 75.27589569122784,
    "fuel": 62.67877735019778,
    "operational": true
  },
  "threat_level": "高",
  "confidence": 0.5735900445851836,
  "last_seen": "2025-09-22T13:29:37.504407",
  "speed": 25.0,
  "heading": 342.77694204457475
}
2025-09-22 13:29:38,221 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.75045907467204,
    "latitude": 39.75224631873955,
    "altitude": 52.1
  },
  "status": {
    "health": 100.0,
    "ammo": 51.62193291667053,
    "fuel": 55.329633512932475,
    "operational": true
  },
  "threat_level": "中",
  "confidence": 0.6353860142826826,
  "last_seen": "2025-09-22T13:29:37.504450",
  "speed": 35.0,
  "heading": 139.5667451225233
}
2025-09-22 13:29:38,221 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 112.3731394830067,
    "latitude": 43.751003784568745,
    "altitude": 8431.80209257034
  },
  "status": {
    "health": 92.73621306295587,
    "ammo": 77.60651064704017,
    "fuel": 51.13264851519357,
    "operational": true
  },
  "threat_level": "极高",
  "confidence": 0.7152205032217335,
  "last_seen": "2025-09-22T13:29:37.504484",
  "speed": 800.0,
  "heading": 31.828521928919052
}
2025-09-22 13:29:38,221 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 13:29:38,222 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 116.40493506913664,
    "latitude": 40.1420092320141,
    "altitude": 48.5
  },
  "status": {
    "health": 84.20868030408457,
    "ammo": 80.70589594827617,
    "fuel": 67.80290271062292,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T13:29:37.504520",
  "speed": 30.0,
  "heading": 357.76211473918534
}
2025-09-22 13:29:38,222 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 111.40027703459809,
    "latitude": 32.79282307772644,
    "altitude": 9359.06807841024
  },
  "status": {
    "health": 91.87781974288777,
    "ammo": 92.87352955991736,
    "fuel": 61.106957901179534,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T13:29:37.504549",
  "speed": 900.0,
  "heading": 221.16649147382125
}
2025-09-22 13:29:38,222 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 13:29:38,222 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 13:29:38,292 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 13:29:38,292 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 13:29:38,294 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 13:29:38,294 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 72.54ms
2025-09-22 13:29:38,294 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 13:29:38,295 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 13:29:38,344 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 13:29:38,344 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 13:29:38,346 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 13:29:38,346 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.86ms
2025-09-22 13:29:38,347 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 13:29:38,347 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 13:29:38,399 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 13:29:38,399 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 13:29:38,401 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 13:29:38,401 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 54.36ms
2025-09-22 13:29:38,401 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 13:29:38,401 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 13:29:38,456 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 13:29:38,456 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 13:29:38,459 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 13:29:38,459 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 57.3ms
2025-09-22 13:29:38,459 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 13:29:38,459 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 13:29:38,508 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 13:29:38,508 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 13:29:38,511 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 13:29:38,511 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 51.88ms
2025-09-22 13:29:38,511 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 13:29:38,511 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 13:29:38,511 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 13:29:38,512 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 13:29:38,512 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 13:29:38,512 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 13:29:38,512 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.5ms
2025-09-22 13:29:38,512 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 13:29:38,512 - main_fastapi - INFO - 用户提示词长度: 2081 字符
2025-09-22 13:29:38,512 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 13:29:38,512 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 13:29:38,561 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 13:29:38,561 - main_fastapi - INFO - LLM服务配置:
2025-09-22 13:29:38,561 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 13:29:38,561 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 13:29:38,562 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 13:29:38,562 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 13:29:38,562 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 13:29:38,562 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 13:29:38,562 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度116.52398971859019, 纬度40.08739464824365, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5735900445851836

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.75045907467204, 纬度39.75224631873955, 高度52.1m
- 威胁等级: 中
- 置信度: 0.6353860142826826

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度112.3731394830067, 纬度43.751003784568745, 高度8431.80209257034m
- 威胁等级: 极高
- 置信度: 0.7152205032217335


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度116.40493506913664, 纬度40.1420092320141, 高度48.5m
- 状态: 可作战
- 健康度: 84.20868030408457%
- 弹药状态: 80.70589594827617%
- 燃料状态: 67.80290271062292%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度111.40027703459809, 纬度32.79282307772644, 高度9359.06807841024m
- 状态: 可作战
- 健康度: 91.87781974288777%
- 弹药状态: 92.87352955991736%
- 燃料状态: 61.106957901179534%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 13:29:38,562 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 13:29:38,562 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 13:29:38,562 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 13:30:16,582 - src.llm_service - INFO - LLM分析完成 - 耗时: 38020.04ms, 消耗tokens: 3105
2025-09-22 13:30:16,582 - main_fastapi - INFO - 大模型分析成功 - 耗时: 38069.91ms
2025-09-22 13:30:16,582 - main_fastapi - INFO - 消耗tokens: 3105
2025-09-22 13:30:16,582 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 13:30:16,582 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 13:30:16,582 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "敌方苏-35战斗机具备强大的空中打击能力，对我方地面部队构成严重威胁；T-90主战坦克拥有强大火力和防护，对我方坦克形成直接挑战。",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "空中优势：苏-35战斗机性能优秀，对我方地面部队构成重大空中威胁。",
      "地面火力：T-90主战坦克火力和防护能力突出，能够有效压制我方坦克。",
      "机动能力：BTR-80装甲运兵车机动性强，可快速调整位置，增强敌方地面灵活性。"
    ],
    "我方优势": [
      "隐形技术：歼-20战斗机具备优异的隐形能力和超音速巡航能力，可在空中占据主动权。",
      "地面装甲：99A主战坦克火力强劲，防护能力出色，且具备一定机动性。",
      "地形利用：平原地形有利于我方机械化部队发挥速度和火力优势。"
    ],
    "关键弱点": [
      "空中防御：我方缺乏有效的防空能力，难以应对敌方战斗机的空中攻击。",
      "地面协调：99A主战坦克与歼-20战斗机之间的协同作战需进一步加强，以实现空地联动。"
    ]
  },
  "战术建议": {
    "推荐策略": "采用空中掩护下的地面集中突破策略，同时通过电子干扰和反导手段削弱敌方空中威胁。",
    "优先目标": "敌方苏-35战斗机（优先空中打击）和T-90主战坦克（优先地面打击）。",
    "兵力部署": "歼-20战斗机负责空中掩护和打击敌方战斗机，同时配合地面部队进行电子干扰和反导任务；99A主战坦克集中力量突破敌方坦克防线，利用地形优势快速推进。",
    "注意事项": [
      "密切关注敌方战斗机动向，及时调整地面部队行进路线。",
      "保持空中与地面部队的通信畅通，确保协同作战效果。",
      "合理分配弹药和燃料，避免因资源不足导致战斗力下降。"
    ]
  },
  "作战方案": [
    {
      "方案名称": "空地联合突破",
      "执行步骤": [
        "第一步：歼-20战斗机迅速升空，利用隐形优势接近敌方苏-35战斗机，实施超视距攻击。",
        "第二步：99A主战坦克在歼-20战斗机空中掩护下，集中火力对敌方T-90主战坦克发起进攻，利用地形隐蔽快速推进。",
        "第三步：一旦敌方空中威胁被压制，99A主战坦克全力突破敌方地面防线，同时歼-20战斗机继续监控并清理残余敌方空中力量。",
        "第四步：利用BTR-80装甲运兵车的薄弱装甲特点，在其可能的机动路线上布设反坦克地雷或伏击点，限制其机动性。"
      ],
      "成功概率": "75%",
      "风险评估": "主要风险在于敌方苏-35战斗机的反击以及T-90主战坦克的集群火力，可能导致我方地面部队损失较大。"
    },
    {
      "方案名称": "诱敌深入",
      "执行步骤": [
        "第一步：99A主战坦克佯攻，吸引敌方T-90主战坦克和BTR-80装甲运兵车集中防御。",
        "第二步：歼-20战斗机利用敌方地面部队注意力分散的时机，隐蔽接近敌方后方机场或指挥中心，实施精准打击。",
        "第三步：待敌方空中力量受损后，99A主战坦克迅速调整方向，绕过敌方正面防线，直取敌方侧翼或后方重要目标。"
      ],
      "成功概率": "65%",
      "风险评估": "此方案风险在于诱饵部队可能遭受较大损失，同时敌方可能识破诱敌计划，导致行动失败。"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地形开阔的特点，选择多条撤退路线，设置伪装障碍物和烟雾弹以迷惑敌人，确保主力部队安全撤离。",
    "支援需求": "请求增派防空导弹部队和电子干扰设备，提升对我方地面部队的保护能力。",
    "备用方案": "如敌方空中优势无法被削弱，立即转入防御姿态，利用地形构筑临时阵地，等待空中支援或后续增援力量到位。"
  }
}
``` 

### 分析说明：
1. **威胁评估**：
   - 敌方的整体威胁等级为“极高”，其中苏-35战斗机和T-90主战坦克是主要威胁来源。
   - 苏-35战斗机的空中打击能力对我方地面部队构成最大威胁，而T-90主战坦克的强大火力和防护能力对99A主战坦克构成直接挑战。

2. **力量对比**：
   - **敌方优势**：空中优势和地面火力强，尤其是苏-35战斗机和T-90主战坦克。
   - **我方优势**：99A主战坦克的火力和防护能力，以及歼-20战斗机的隐形和超音速巡航能力。
   - **关键弱点**：我方缺乏有效防空能力，难以应对敌方空中威胁；地面与空中部队的协同作战需进一步加强。

3. **战术建议**：
   - 推荐采用空中掩护下的地面集中突破策略，优先打击敌方空中和地面核心力量。
   - 注意事项包括空中与地面部队的通信畅通、资源管理以及灵活应对敌方机动。

4. **作战方案**：
   - **空地联合突破**：歼-20负责空中掩护和打击，99A主战坦克集中突破，成功率较高但风险在于敌方空中反击。
   - **诱敌深入**：利用佯攻吸引敌方注意力，歼-20趁机打击敌方后方，成功率稍低但更具战略意义。

5. **应急预案**：
   - 如遇不利局势，需制定撤退路线，请求增派防空和电子干扰支援，必要时转入防御姿态，等待后续支援。
2025-09-22 13:30:16,582 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 13:30:16,582 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 13:30:16,583 - main_fastapi - INFO - 向 36 个客户端广播消息
2025-09-22 13:30:16,583 - main_fastapi - ERROR - 广播消息失败: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-09-22 13:30:16,583 - main_fastapi - ERROR - 广播消息失败: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-09-22 13:30:16,583 - main_fastapi - ERROR - 广播消息失败: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-09-22 13:30:16,583 - main_fastapi - ERROR - 广播消息失败: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-09-22 13:30:16,584 - main_fastapi - ERROR - 广播消息失败: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-09-22 13:30:16,584 - main_fastapi - ERROR - 广播消息失败: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-09-22 13:30:18,758 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 37
2025-09-22 13:30:26,584 - main_fastapi - ERROR - 广播消息失败: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-09-22 13:30:26,584 - main_fastapi - ERROR - 广播分析结果失败: Set changed size during iteration
2025-09-22 13:30:26,584 - main_fastapi - INFO - ================================================================================
2025-09-22 13:30:26,584 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758518978221
2025-09-22 13:30:26,584 - main_fastapi - INFO - 总处理时间: 38361.42ms
2025-09-22 13:30:26,584 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 13:30:26,584 - main_fastapi - INFO - 消耗tokens: 3105
2025-09-22 13:30:26,584 - main_fastapi - INFO - ================================================================================
2025-09-22 13:30:26,585 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758518978221_20250922_133026.json
