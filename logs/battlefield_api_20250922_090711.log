2025-09-22 09:07:11,676 - main_fastapi - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250922_090711.log
2025-09-22 09:07:11,676 - main_fastapi - INFO - 战场态势分析API服务启动
2025-09-22 09:07:18,719 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 1
2025-09-22 09:07:20,722 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 2
2025-09-22 09:07:20,722 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 3
2025-09-22 09:07:23,994 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 4
2025-09-22 09:08:32,892 - main_fastapi - INFO - ================================================================================
2025-09-22 09:08:32,892 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758503312892
2025-09-22 09:08:32,892 - main_fastapi - INFO - 模拟时间: 60.0分钟
2025-09-22 09:08:32,892 - main_fastapi - INFO - ================================================================================
2025-09-22 09:08:32,892 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 09:08:32,892 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 09:08:32,892 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 116.62355775419314,
    "latitude": 40.042840095987536,
    "altitude": 45.2
  },
  "status": {
    "health": 87.36225036725433,
    "ammo": 75.48904372210944,
    "fuel": 60.452526843130805,
    "operational": true
  },
  "threat_level": "高",
  "confidence": 0.6547223218022533,
  "last_seen": "2025-09-22T09:08:32.823236",
  "speed": 25.0,
  "heading": 82.91285210465409
}
2025-09-22 09:08:32,892 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.63807327914263,
    "latitude": 39.672077613757914,
    "altitude": 52.1
  },
  "status": {
    "health": 100.0,
    "ammo": 59.03169938253053,
    "fuel": 57.32879870932968,
    "operational": true
  },
  "threat_level": "中",
  "confidence": 0.5894445612237917,
  "last_seen": "2025-09-22T09:08:32.823284",
  "speed": 35.0,
  "heading": 157.36155172819133
}
2025-09-22 09:08:32,892 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 107.1946691089253,
    "latitude": 39.00348248908955,
    "altitude": 7992.296879823405
  },
  "status": {
    "health": 100.0,
    "ammo": 75.62607796599991,
    "fuel": 48.64265602237972,
    "operational": true
  },
  "threat_level": "极高",
  "confidence": 0.682250456606324,
  "last_seen": "2025-09-22T09:08:32.823319",
  "speed": 800.0,
  "heading": 261.8852468305057
}
2025-09-22 09:08:32,892 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 09:08:32,893 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 116.13401391040271,
    "latitude": 40.0771770967392,
    "altitude": 48.5
  },
  "status": {
    "health": 96.44096236339476,
    "ammo": 86.401338688232,
    "fuel": 65.02522873908063,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T09:08:32.823348",
  "speed": 30.0,
  "heading": 306.4457810674324
}
2025-09-22 09:08:32,893 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 122.0437019670074,
    "latitude": 33.616480757242726,
    "altitude": 9415.410992365225
  },
  "status": {
    "health": 100.0,
    "ammo": 94.50853846672183,
    "fuel": 62.19178368349608,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T09:08:32.823377",
  "speed": 900.0,
  "heading": 132.08095521487067
}
2025-09-22 09:08:32,893 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 09:08:32,893 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 09:08:33,248 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:08:33,248 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:08:33,251 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 09:08:33,251 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 358.17ms
2025-09-22 09:08:33,251 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 09:08:33,256 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 09:08:33,588 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:08:33,588 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:08:33,590 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 09:08:33,590 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 334.89ms
2025-09-22 09:08:33,591 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 09:08:33,595 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 09:08:33,927 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:08:33,927 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:08:33,929 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 09:08:33,929 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 334.61ms
2025-09-22 09:08:33,929 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 09:08:33,936 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 09:08:34,267 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:08:34,268 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:08:34,272 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 09:08:34,272 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 336.05ms
2025-09-22 09:08:34,272 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 09:08:34,281 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 09:08:34,621 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:08:34,622 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:08:34,624 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 09:08:34,624 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 343.24ms
2025-09-22 09:08:34,625 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 09:08:34,631 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 09:08:34,633 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 09:08:34,638 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 09:08:34,638 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 09:08:34,638 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 09:08:34,638 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 5.11ms
2025-09-22 09:08:34,638 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 09:08:34,638 - main_fastapi - INFO - 用户提示词长度: 2068 字符
2025-09-22 09:08:34,638 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 09:08:34,638 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 09:08:34,674 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 09:08:34,674 - main_fastapi - INFO - LLM服务配置:
2025-09-22 09:08:34,674 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 09:08:34,674 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 09:08:34,674 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 09:08:34,674 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 09:08:34,674 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 09:08:34,674 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 09:08:34,674 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度116.62355775419314, 纬度40.042840095987536, 高度45.2m
- 威胁等级: 高
- 置信度: 0.6547223218022533

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.63807327914263, 纬度39.672077613757914, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5894445612237917

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度107.1946691089253, 纬度39.00348248908955, 高度7992.296879823405m
- 威胁等级: 极高
- 置信度: 0.682250456606324


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度116.13401391040271, 纬度40.0771770967392, 高度48.5m
- 状态: 可作战
- 健康度: 96.44096236339476%
- 弹药状态: 86.401338688232%
- 燃料状态: 65.02522873908063%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度122.0437019670074, 纬度33.616480757242726, 高度9415.410992365225m
- 状态: 可作战
- 健康度: 100.0%
- 弹药状态: 94.50853846672183%
- 燃料状态: 62.19178368349608%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 09:08:34,674 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 09:08:34,674 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 09:08:34,674 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 09:09:09,854 - src.llm_service - INFO - LLM分析完成 - 耗时: 35179.25ms, 消耗tokens: 2988
2025-09-22 09:09:09,854 - main_fastapi - INFO - 大模型分析成功 - 耗时: 35215.45ms
2025-09-22 09:09:09,854 - main_fastapi - INFO - 消耗tokens: 2988
2025-09-22 09:09:09,854 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 09:09:09,854 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 09:09:09,854 - main_fastapi - INFO - 以下是针对当前战场态势的详细分析结果，按照要求以 JSON 格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "苏-35战斗机（空中威胁）和T-90主战坦克（地面威胁）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "T-90主战坦克具备强大的火力和防护能力，适合集群作战",
      "苏-35战斗机拥有出色的机动性和超音速巡航能力，威胁范围广"
    ],
    "我方优势": [
      "99A主战坦克具有先进火控系统和良好的机动性能",
      "歼-20战斗机具备隐身能力和超音速巡航能力，制空能力突出"
    ],
    "关键弱点": [
      "敌方BTR-80装甲运兵车数量可能较多，对我方造成步兵威胁",
      "我方99A主战坦克与T-90主战坦克在防护和火力上接近，但机动性稍弱"
    ]
  },
  "战术建议": {
    "推荐策略": "联合防空与地面进攻相结合，优先打击敌方空中威胁，随后集中力量摧毁敌方地面装甲",
    "优先目标": "苏-35战斗机（优先空中打击），其次为T-90主战坦克（地面火力压制）",
    "兵力部署": "歼-20战斗机负责空中掩护和优先打击敌方战斗机；99A主战坦克机动至敌方T-90主战坦克后方进行侧翼包抄",
    "注意事项": [
      "避免正面硬碰硬，利用地形进行隐蔽机动",
      "密切监控敌方战斗机动向，防止被其偷袭",
      "注意保持通讯畅通，及时调整战术"
    ]
  },
  "作战方案": [
    {
      "方案名称": "空中优先打击+地面侧翼包抄",
      "执行步骤": [
        "第一步：歼-20战斗机迅速升空，利用隐身优势靠近苏-35战斗机，使用超视距导弹进行首轮打击",
        "第二步：99A主战坦克利用平原地形快速机动到敌方T-90主战坦克侧翼，避开其正面火力，准备展开攻击",
        "第三步：一旦空中威胁解除，99A主战坦克立即发起对T-90主战坦克的集中火力打击，同时注意防范BTR-80装甲运兵车的反击"
      ],
      "成功概率": "75%",
      "风险评估": "若歼-20未能有效压制苏-35战斗机，可能导致地面部队暴露于空中威胁之下；BTR-80装甲运兵车可能从侧面威胁我方坦克"
    },
    {
      "方案名称": "地面诱饵+空中掩护",
      "执行步骤": [
        "第一步：派出少量地面部队作为诱饵，吸引敌方T-90主战坦克和BTR-80装甲运兵车的注意力",
        "第二步：歼-20战斗机在高空进行巡逻，随时准备打击敌方战斗机或支援地面部队",
        "第三步：99A主战坦克在诱饵部队的掩护下，绕道敌方主力后方，集中火力打击T-90主战坦克"
      ],
      "成功概率": "65%",
      "风险评估": "诱饵部队可能遭受较大损失，且敌方可能识破诱饵战术，导致地面部队陷入被动"
    }
  ],
  "应急预案": {
    "撤退路线": "利用平原地形分散撤退，每个单位选择不同方向，减少集中暴露风险",
    "支援需求": "请求空中加油支援（为歼-20战斗机补充燃料）、地面反坦克导弹支援（增强对T-90主战坦克的打击能力）",
    "备用方案": "若空中无法压制敌方战斗机，则暂时放弃地面进攻，集中歼-20战斗机打击敌方战斗机，争取制空权后再进行地面行动"
  }
}
```

### 解析说明：
1. **威胁评估**：
   - 整体威胁等级为“极高”，因为敌方拥有苏-35战斗机这种高性能战斗机以及T-90主战坦克这样的强火力地面单位。
   - 主要威胁是苏-35战斗机和T-90主战坦克，它们分别在空中和地面构成最大威胁。

2. **力量对比**：
   - 敌方在地面和空中都有较强的实力，尤其是T-90和苏-35的组合。
   - 我方的优势在于99A主战坦克的先进火控和歼-20战斗机的隐身与超音速能力，但需要注意地面机动性和空中掩护的协调。

3. **战术建议**：
   - 首先解决空中威胁，再集中力量对付地面装甲，避免陷入两线作战的被动局面。
   - 优先目标是苏-35战斗机，因为它是对我方地面部队的最大威胁。

4. **作战方案**：
   - 方案一侧重于空中优先打击，利用歼-20的优势快速压制敌方战斗机，随后99A主战坦克进行地面突击。
   - 方案二则通过地面诱饵吸引敌方注意力，为99A主战坦克创造侧翼包抄的机会，但存在一定风险。

5. **应急预案**：
   - 若作战不利，应迅速分散撤退，避免被敌方集中火力打击。
   - 请求空中加油和地面反坦克导弹支援，增强持续作战能力。
   - 如果空中压制失败，可以暂时放弃地面进攻，全力争取制空权，为后续行动创造条件。

此分析综合考虑了敌我双方的优势与劣势，结合当前战场环境提出了切实可行的战术建议和应急预案，旨在最大化我方优势并降低风险。
2025-09-22 09:09:09,854 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 09:09:09,854 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 09:09:09,854 - main_fastapi - INFO - 向 4 个客户端广播消息
2025-09-22 09:09:09,856 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758503312892
2025-09-22 09:09:09,856 - main_fastapi - INFO - ================================================================================
2025-09-22 09:09:09,856 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758503312892
2025-09-22 09:09:09,856 - main_fastapi - INFO - 总处理时间: 36962.28ms
2025-09-22 09:09:09,856 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 09:09:09,856 - main_fastapi - INFO - 消耗tokens: 2988
2025-09-22 09:09:09,856 - main_fastapi - INFO - ================================================================================
2025-09-22 09:09:09,857 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758503312892_20250922_090909.json
2025-09-22 09:09:34,315 - main_fastapi - INFO - ================================================================================
2025-09-22 09:09:34,315 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758503374315
2025-09-22 09:09:34,315 - main_fastapi - INFO - 模拟时间: 120.0分钟
2025-09-22 09:09:34,315 - main_fastapi - INFO - ================================================================================
2025-09-22 09:09:34,315 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-22 09:09:34,315 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-22 09:09:34,315 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 116.79411009837781,
    "latitude": 39.9015080978778,
    "altitude": 45.2
  },
  "status": {
    "health": 82.27253141418606,
    "ammo": 56.04035737802758,
    "fuel": 34.27822337067462,
    "operational": true
  },
  "threat_level": "高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T09:09:34.243685",
  "speed": 25.0,
  "heading": 184.02013205477894
}
2025-09-22 09:09:34,315 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.93871185551696,
    "latitude": 39.57242400767525,
    "altitude": 52.1
  },
  "status": {
    "health": 94.36016250630995,
    "ammo": 59.03169938253053,
    "fuel": 30.68796396139175,
    "operational": true
  },
  "threat_level": "中",
  "confidence": 0.5018024096452923,
  "last_seen": "2025-09-22T09:09:34.243734",
  "speed": 35.0,
  "heading": 71.27618505448345
}
2025-09-22 09:09:34,315 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 97.92974492193385,
    "latitude": 38.22567236691668,
    "altitude": 8542.135896065714
  },
  "status": {
    "health": 84.85137179542478,
    "ammo": 67.68195741889534,
    "fuel": 24.39986947209589,
    "operational": true
  },
  "threat_level": "极高",
  "confidence": 0.5,
  "last_seen": "2025-09-22T09:09:34.243767",
  "speed": 800.0,
  "heading": 294.47185613625015
}
2025-09-22 09:09:34,315 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-22 09:09:34,316 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 116.0321087970235,
    "latitude": 40.31862841367941,
    "altitude": 48.5
  },
  "status": {
    "health": 96.44096236339476,
    "ammo": 82.7756805046193,
    "fuel": 41.75032909084649,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T09:09:34.243795",
  "speed": 30.0,
  "heading": 17.314070450600184
}
2025-09-22 09:09:34,316 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 126.08212431490658,
    "latitude": 26.388214817252468,
    "altitude": 9898.157514647062
  },
  "status": {
    "health": 91.31603562929818,
    "ammo": 83.6579064516868,
    "fuel": 39.69501514718487,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-22T09:09:34.243822",
  "speed": 900.0,
  "heading": 180.93894915273214
}
2025-09-22 09:09:34,316 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-22 09:09:34,316 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-22 09:09:34,654 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:09:34,654 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:09:34,657 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-22 09:09:34,657 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 340.96ms
2025-09-22 09:09:34,657 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 09:09:34,662 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-22 09:09:35,009 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:09:35,009 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:09:35,012 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-22 09:09:35,012 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 349.94ms
2025-09-22 09:09:35,012 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 09:09:35,018 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-22 09:09:35,359 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:09:35,360 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:09:35,365 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-22 09:09:35,365 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 346.88ms
2025-09-22 09:09:35,365 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 09:09:35,372 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-22 09:09:35,705 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:09:35,705 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:09:35,708 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-22 09:09:35,710 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 338.8ms
2025-09-22 09:09:35,710 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-22 09:09:35,714 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-22 09:09:36,050 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-22 09:09:36,050 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-22 09:09:36,053 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-22 09:09:36,053 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 338.29ms
2025-09-22 09:09:36,053 - main_fastapi - INFO - 查询结果状态: unknown
2025-09-22 09:09:36,057 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-22 09:09:36,059 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-22 09:09:36,059 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-22 09:09:36,059 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-22 09:09:36,059 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-22 09:09:36,059 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.24ms
2025-09-22 09:09:36,059 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-22 09:09:36,059 - main_fastapi - INFO - 用户提示词长度: 2049 字符
2025-09-22 09:09:36,059 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-22 09:09:36,059 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-22 09:09:36,060 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-22 09:09:36,060 - main_fastapi - INFO - LLM服务配置:
2025-09-22 09:09:36,060 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-22 09:09:36,060 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 09:09:36,060 - main_fastapi - INFO -   超时设置: 60s
2025-09-22 09:09:36,060 - main_fastapi - INFO - === 系统提示词 ===
2025-09-22 09:09:36,060 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-22 09:09:36,060 - main_fastapi - INFO - === 用户提示词 ===
2025-09-22 09:09:36,060 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度116.79411009837781, 纬度39.9015080978778, 高度45.2m
- 威胁等级: 高
- 置信度: 0.5

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.93871185551696, 纬度39.57242400767525, 高度52.1m
- 威胁等级: 中
- 置信度: 0.5018024096452923

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度97.92974492193385, 纬度38.22567236691668, 高度8542.135896065714m
- 威胁等级: 极高
- 置信度: 0.5


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度116.0321087970235, 纬度40.31862841367941, 高度48.5m
- 状态: 可作战
- 健康度: 96.44096236339476%
- 弹药状态: 82.7756805046193%
- 燃料状态: 41.75032909084649%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度126.08212431490658, 纬度26.388214817252468, 高度9898.157514647062m
- 状态: 可作战
- 健康度: 91.31603562929818%
- 弹药状态: 83.6579064516868%
- 燃料状态: 39.69501514718487%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-22 09:09:36,060 - main_fastapi - INFO - === 提示词结束 ===
2025-09-22 09:09:36,060 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-22 09:09:36,060 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-22 09:10:11,435 - src.llm_service - INFO - LLM分析完成 - 耗时: 35374.72ms, 消耗tokens: 2976
2025-09-22 09:10:11,435 - main_fastapi - INFO - 大模型分析成功 - 耗时: 35375.36ms
2025-09-22 09:10:11,435 - main_fastapi - INFO - 消耗tokens: 2976
2025-09-22 09:10:11,435 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-22 09:10:11,435 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-22 09:10:11,435 - main_fastapi - INFO - 以下是基于战场态势的详细分析结果，以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "高",
    "主要威胁": "苏-35战斗机（空中威胁）和T-90主战坦克（地面威胁）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "苏-35战斗机具备强大的空中打击能力，且处于高空警戒状态",
      "T-90主战坦克拥有强大的火力和防护能力，适合集群作战"
    ],
    "我方优势": [
      "歼-20战斗机具备隐身能力和超音速巡航能力，可在空中占据优势",
      "99A主战坦克性能均衡，火力和防护能力优异，机动性较强"
    ],
    "关键弱点": [
      "敌方苏-35战斗机在空中对我方构成极大威胁，需优先处理",
      "我方地面部队可能面临敌方坦克和装甲车的协同进攻"
    ]
  },
  "战术建议": {
    "推荐策略": "空中优先压制，地面集中突破，协同作战",
    "优先目标": "苏-35战斗机（首要空中威胁），其次为T-90主战坦克（首要地面威胁）",
    "兵力部署": "歼-20战斗机负责空中制空权争夺，同时掩护地面部队；99A主战坦克集中力量突破敌方坦克防线，配合空中支援",
    "注意事项": [
      "确保空中力量的通信和导航设备正常工作，避免被敌方电子干扰",
      "地面部队应注意隐蔽行进，避免暴露在敌方坦克的直接火力范围内",
      "随时准备应对敌方可能的空中支援或增援"
    ]
  },
  "作战方案": [
    {
      "方案名称": "空中先发制人，地面集中突破",
      "执行步骤": [
        "第一步：歼-20战斗机迅速接近苏-35战斗机，利用隐身优势发起超视距攻击，争取快速击落对方战机",
        "第二步：99A主战坦克向敌方T-90主战坦克方向推进，利用地形隐蔽，同时保持与歼-20的通信联系，请求空中支援",
        "第三步：一旦空中优势确立，99A主战坦克加速推进，利用火力优势突破敌方坦克防线，同时注意防范BTR-80装甲车的侧翼威胁"
      ],
      "成功概率": "70%",
      "风险评估": "若歼-20未能有效压制敌方苏-35战斗机，可能导致地面部队遭受重大损失；敌方可能通过电子干扰或防空网反击，影响我方空中优势"
    },
    {
      "方案名称": "地面牵制，空中掩护",
      "执行步骤": [
        "第一步：99A主战坦克佯攻敌方T-90主战坦克，吸引其火力并消耗其弹药，同时保持距离，避免正面冲突",
        "第二步：歼-20战斗机在高空保持警戒，随时准备拦截敌方可能的空中增援或反击",
        "第三步：地面部队逐步推进，寻找敌方防线薄弱环节，寻求突破机会，同时利用空中支援进行定点清除"
      ],
      "成功概率": "60%",
      "风险评估": "地面佯攻可能导致弹药消耗过快，且敌方可能识破意图，调整部署；空中掩护需要持续保持高度警惕，防止敌方突然袭击"
    }
  ],
  "应急预案": {
    "撤退路线": "地面部队向北撤退至预设集结点，空中部队返回后方基地，确保脱离敌方火力范围",
    "支援需求": "请求空中加油机支援，保障歼-20的续航能力；地面部队需要补充弹药和燃油",
    "备用方案": "若空中制空权无法确立，地面部队暂时转入防御姿态，等待后续支援力量到达"
  }
}
```

### 分析说明：
1. **威胁评估**：
   - 敌方的苏-35战斗机和T-90主战坦克分别构成空中和地面的主要威胁。
   - 敌方BTR-80装甲车虽然威胁等级中等，但其高机动性和载员能力也不可忽视。

2. **力量对比**：
   - 敌方优势在于苏-35战斗机的强大空中打击能力和T-90主战坦克的坚固防护及火力。
   - 我方优势在于歼-20战斗机的隐身和超音速能力，以及99A主战坦克的综合性能。
   - 关键弱点在于敌方空中优势可能对地面部队造成致命威胁，而我方地面部队推进时需小心敌方坦克的火力覆盖。

3. **战术建议**：
   - 空中优先压制敌方苏-35战斗机，确保制空权，为地面部队创造有利条件。
   - 地面部队集中力量突破敌方坦克防线，同时注意敌方装甲运兵车的侧翼威胁。

4. **作战方案**：
   - 方案一侧重于空中先发制人，通过歼-20的隐身和超音速能力快速压制敌方空中力量，随后地面部队集中突破。
   - 方案二则采取地面牵制策略，利用佯攻吸引敌方注意力，同时空中掩护确保地面部队安全。

5. **应急预案**：
   - 确保有明确的撤退路线，尤其是在空中制空权未确立或地面部队遭遇重大阻碍的情况下。
   - 请求空中加油、弹药补给等支援，保障持续作战能力。
   - 准备备用方案，如地面转入防御姿态，等待后续支援力量。

此分析综合考虑了战场环境、敌我双方力量对比以及关键威胁和机会，提供了切实可行的战术建议和风险评估，以确保作战行动的高效性和安全性。
2025-09-22 09:10:11,435 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-22 09:10:11,435 - main_fastapi - WARNING - 解析LLM分析结果失败: Expecting value: line 1 column 1 (char 0)
2025-09-22 09:10:11,435 - main_fastapi - INFO - 向 4 个客户端广播消息
2025-09-22 09:10:11,436 - main_fastapi - ERROR - 广播消息失败: received 1011 (internal error) keepalive ping timeout; then sent 1011 (internal error) keepalive ping timeout
2025-09-22 09:10:11,436 - main_fastapi - INFO - WebSocket连接已断开，当前连接数: 3
2025-09-22 09:10:11,436 - main_fastapi - INFO - 已广播分析结果更新: ANALYSIS_1758503374315
2025-09-22 09:10:11,436 - main_fastapi - INFO - ================================================================================
2025-09-22 09:10:11,436 - main_fastapi - INFO - 战场态势分析完成 - ID: ANALYSIS_1758503374315
2025-09-22 09:10:11,436 - main_fastapi - INFO - 总处理时间: 37120.25ms
2025-09-22 09:10:11,437 - main_fastapi - INFO - LLM分析状态: success
2025-09-22 09:10:11,437 - main_fastapi - INFO - 消耗tokens: 2976
2025-09-22 09:10:11,437 - main_fastapi - INFO - ================================================================================
2025-09-22 09:10:11,438 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758503374315_20250922_091011.json
2025-09-22 09:10:17,925 - main_fastapi - INFO - 新的WebSocket连接已建立，当前连接数: 4
